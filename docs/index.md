# Scaf Documentation

Welcome to the Scaf documentation! This documentation provides comprehensive information about the Scaf codebase, its architecture, and development workflows.

## Documentation Structure

1. **[Getting Started](./getting-started.md)** - Quick setup and orientation
2. **[Development Workflows](./development-workflows.md)** - Common development tasks (new features, bug fixes)
3. **[Architecture Overview](./architecture-overview.md)** - Consolidated architecture information
4. **[State Management](./state-management.md)** - Consolidated pumped-fn pattern documentation
5. **[API Integration](./api-integration.md)** - Backend-frontend communication

## Key Concepts

### Base-Mod Pattern

The repository follows a modular architecture with a clear separation between reusable infrastructure (`base`) and application-specific customizations (`mod`).

### Pumped-fn Pattern

The repository uses pumped-fn's `provide`/`derive` pattern for state management and dependency injection, enabling reactive UI updates and clean separation of concerns.

### Reactive Components

React components use `@pumped-fn/react` to subscribe to state changes, with components only re-rendering when their specific reactive dependencies change.

## Technology Stack

- **Runtime**: Bun
- **Frontend**: React, Tailwind CSS, DaisyUI
- **State Management**: pumped-fn
- **Backend**: Bun server
- **Database**: PostgreSQL with Drizzle ORM

