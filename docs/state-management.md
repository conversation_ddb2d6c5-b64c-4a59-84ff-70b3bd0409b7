# State Management with Pumped-fn

The Scaf codebase uses the pumped-fn pattern for state management and dependency injection, providing a unified approach for both frontend and backend code.

## Core Concepts

### provide and derive

The pumped-fn pattern is built around two core functions: `provide` and `derive`.

```typescript
import { provide, derive } from "@pumped-fn/core-next";

// Create state
export const users = provide(() => initialUsers);

// Create derived state - executes ONLY when dependencies change
export const filteredUsers = derive(
  [users.reactive, searchTerm.reactive],
  ([users, searchTerm]) => users.filter(user => 
    user.name.includes(searchTerm)
  )
);

// Create controller
export const setSearchTerm = derive(
  [searchTerm.static],
  ([searchTermCtl]) => (newTerm) => searchTermCtl.update(newTerm)
);
```

### Reactive Execution Model

The key feature of pumped-fn is its reactive execution model:

1. **Reactive Dependencies** (`.reactive`): Trigger re-execution when they change
2. **Static Dependencies** (`.static`): Access controllers without triggering re-execution

Functions only execute when their reactive dependencies change.

## State Definition

### Basic State

```typescript
// Define initial state
export const count = provide(() => 0);

// Create controllers to update state
export const increment = derive(
  [count.static],
  ([countCtl]) => () => countCtl.update(c => c + 1)
);

export const decrement = derive(
  [count.static],
  ([countCtl]) => () => countCtl.update(c => c - 1)
);

export const reset = derive(
  [count.static],
  ([countCtl]) => () => countCtl.update(0)
);
```

### Derived State

```typescript
// Define initial states
export const todos = provide(() => []);
export const filter = provide(() => "all");

// Create derived state
export const filteredTodos = derive(
  [todos.reactive, filter.reactive],
  ([todos, filter]) => {
    switch (filter) {
      case "active":
        return todos.filter(todo => !todo.completed);
      case "completed":
        return todos.filter(todo => todo.completed);
      default:
        return todos;
    }
  }
);

// Create controllers
export const addTodo = derive(
  [todos.static],
  ([todosCtl]) => (text) => {
    todosCtl.update(todos => [
      ...todos,
      { id: Date.now(), text, completed: false }
    ]);
  }
);

export const toggleTodo = derive(
  [todos.static],
  ([todosCtl]) => (id) => {
    todosCtl.update(todos => todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  }
);

export const setFilter = derive(
  [filter.static],
  ([filterCtl]) => (newFilter) => filterCtl.update(newFilter)
);
```

### Async State

```typescript
// Define initial state
export const users = provide(() => ({ loading: false, data: [], error: null }));

// Create controller to fetch users
export const fetchUsers = derive(
  [users.static],
  ([usersCtl]) => async () => {
    // Set loading state
    usersCtl.update(state => ({ ...state, loading: true }));
    
    try {
      // Fetch data
      const response = await fetch("https://api.example.com/users");
      const data = await response.json();
      
      // Update state with data
      usersCtl.update(state => ({ loading: false, data, error: null }));
    } catch (error) {
      // Update state with error
      usersCtl.update(state => ({ loading: false, data: [], error }));
    }
  }
);
```

## React Integration

### Resolves Component

The `Resolves` component is used to subscribe to state changes:

```typescript
import { Resolves } from "@pumped-fn/react";
import { count, increment, decrement, reset } from "./state";

const Counter = () => (
  <Resolves e={[count.reactive, increment, decrement, reset]}>
    {([count, increment, decrement, reset]) => (
      <div>
        <h1>Count: {count}</h1>
        <button onClick={increment}>Increment</button>
        <button onClick={decrement}>Decrement</button>
        <button onClick={reset}>Reset</button>
      </div>
    )}
  </Resolves>
);
```

### useResolves Hook

The `useResolves` hook is an alternative to the `Resolves` component:

```typescript
import { useResolves } from "@pumped-fn/react";
import { count, increment, decrement, reset } from "./state";

const Counter = () => {
  const [count, increment, decrement, reset] = useResolves(
    count.reactive,
    increment,
    decrement,
    reset
  );
  
  return (
    <div>
      <h1>Count: {count}</h1>
      <button onClick={increment}>Increment</button>
      <button onClick={decrement}>Decrement</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
};
```

## Dependency Chain

Changes propagate through the dependency chain:

```typescript
// When selectedUserId changes:
// 1. selectedUser recalculates
// 2. userTodos recalculates
// 3. Components using userTodos.reactive update

export const selectedUserId = provide(() => undefined);

export const selectedUser = derive(
  [users.reactive, selectedUserId.reactive],
  ([users, userId]) => users.find(user => user.id === userId)
);

export const userTodos = derive(
  [selectedUser.reactive, todos.reactive],
  ([user, todos]) => todos.filter(todo => todo.userId === user?.id)
);
```

## Unified Export Pattern

```typescript
export const app = {
  users,
  todos,
  selectedUser,
  setSelectedUser,
  userTodos,
  todosCtl,
  // Include effects that need to be resolved
  updateStoreEffect
};
```

## Backend Integration

Pumped-fn is also used for backend services, providing a consistent pattern:

```typescript
// Database connection using pumped-fn
export const connection = derive(
  [config, logger('db')], 
  async ([config, logger], ctl) => {
    logger.info(`Connecting to database...`);
    const db = drizzle({
      connection: {
        connectionString: `postgres://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`,
        ssl: false
      }
    });
    
    // Test connection
    await db.execute("SELECT 1");
    logger.debug('db connection established successfully');
    return db;
  }
);

// API route implementation using pumped-fn
export const createTodoRoute = router.implements(
  'createTodo',
  derive(
    [connection, logger('route.createTodo')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Creating todo with input:', input);
      await db.insert(todos).values(input);
    }
  )
);
```

## Advanced Patterns

### Effects

Effects are used to perform side effects when dependencies change:

```typescript
import { effect } from "@pumped-fn/core-next";

// Log when count changes
export const logCountEffect = effect(
  [count.reactive],
  ([count]) => {
    console.log(`Count changed to ${count}`);
  }
);

// Save todos to localStorage when they change
export const saveTodosEffect = effect(
  [todos.reactive],
  ([todos]) => {
    localStorage.setItem("todos", JSON.stringify(todos));
  }
);
```

### Lazy Initialization

```typescript
// Lazy initialization with a factory function
export const expensiveState = provide(() => {
  console.log("Initializing expensive state");
  return computeExpensiveInitialState();
});

// Lazy initialization with localStorage
export const persistedState = provide(() => {
  const stored = localStorage.getItem("persistedState");
  return stored ? JSON.parse(stored) : defaultState;
});
```

### Cleanup

```typescript
// Cleanup when dependencies change
export const subscription = derive(
  [userId.reactive],
  async ([userId], ctl) => {
    const subscription = api.subscribe(userId, (data) => {
      // Handle data
    });
    
    // Return cleanup function
    return () => {
      subscription.unsubscribe();
    };
  }
);
```

## Key Benefits

1. **Explicit Dependencies**: Dependencies are clearly declared
2. **Selective Reactivity**: Functions only execute when dependencies change
3. **Separation of Concerns**: State definition separate from consumption
4. **Type Safety**: TypeScript ensures type safety throughout
5. **Unified Pattern**: Same pattern for frontend and backend code
6. **Testability**: Easy to test in isolation with dependency injection

## Further Reading

- [Development Workflows](./development-workflows.md): Guide to common development tasks
- [API Integration](./api-integration.md): Details on backend-frontend communication

