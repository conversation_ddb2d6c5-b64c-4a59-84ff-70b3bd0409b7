# Development Workflows

This guide covers common development workflows for the Scaf codebase, focusing on how to add new features and fix bugs efficiently.

## Adding a New Feature

### 1. Understand the Feature Requirements

Before writing any code, make sure you understand:
- What problem the feature solves
- How it should behave
- How it fits into the existing architecture

### 2. Plan Your Changes

Determine which parts of the codebase need to be modified:

1. **Database Changes**
   - Do you need to add new tables or modify existing ones?
   - Update schema in `mod/backend/schema.ts`
   - Update shapes in `mod/backend/shapes.ts`

2. **Shared Types**
   - Define shared types in `mod/dual/index.types.zod.ts`
   - Use Zod for validation

3. **Backend API**
   - Define RPC endpoints in `mod/dual/rpc.ts`
   - Implement endpoints in `mod/backend/routes.ts`

4. **Frontend State**
   - Create or update state in `mod/frontend/pumped.*.ts` files
   - Define controllers for state manipulation

5. **UI Components**
   - Create or update components in `mod/frontend/components/`

### 3. Implementation Workflow

#### Step 1: Database Changes

If your feature requires database changes:

@[example](../examples/database-schema.ts)

Then run migrations:

```bash
bun run script db:generate
bun run script db:migrate
```

#### Step 2: Define Shared Types

@[example](../examples/shared-types.ts)

#### Step 3: Define RPC Endpoints

@[example](../examples/rpc-endpoints.ts)

#### Step 4: Implement Backend Routes

@[example](../examples/backend-routes.ts)

#### Step 5: Create Frontend State

@[example](../examples/frontend-state.ts)

#### Step 6: Create UI Components

@[example](../examples/ui-component.tsx)

#### Step 7: Add Component to Page

@[example](../examples/app-component.tsx)

## Fixing Bugs

### 1. Understand the Bug

- Reproduce the bug to understand its behavior
- Identify which part of the codebase is affected
- Check if there are any related issues or PRs

### 2. Locate the Source

Use the following strategies to locate the source of the bug:

- Check error messages and stack traces
- Use browser developer tools for frontend issues
- Check server logs for backend issues
- Add temporary logging to trace the execution flow

### 3. Fix the Bug

Once you've located the source of the bug:

1. Make minimal changes to fix the issue
2. Update documentation if necessary
3. Verify that your changes don't introduce new bugs

### 4. Test the Fix

- Verify that the bug is fixed
- Ensure that your changes don't introduce new bugs
- Run existing tests to ensure nothing else is broken

## Deployment

### Building for Production

```bash
bun run script build
```

### Running in Production

```bash
bun run script start
```

## Additional Resources

- See [Architecture Overview](./architecture-overview.md) for a deeper understanding of the codebase structure
- See [State Management](./state-management.md) for details on the pumped-fn pattern
- See [API Integration](./api-integration.md) for more information on backend-frontend communication

