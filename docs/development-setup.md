# Development Setup Guide

This guide will help you set up and run the Scaf application with Better Auth integration locally.

## Prerequisites

Before starting, ensure you have the following installed:

- **[Bun](https://bun.sh/)** (latest version) - JavaScript runtime and package manager
- **[PostgreSQL](https://www.postgresql.org/)** (version 12+) - Database server
- **[Git](https://git-scm.com/)** - Version control

## Quick Start

### 1. <PERSON>lone and Install

```bash
# Clone the repository
git clone <repository-url>
cd scaf

# Install dependencies
bun install
```

### 2. Database Setup

Start PostgreSQL and create a database:

```bash
# Start PostgreSQL (method varies by OS)
# macOS with Homebrew:
brew services start postgresql

# Create database (if not exists)
createdb postgres
```

### 3. Environment Configuration

The `.env` file is already configured with default values:

```env
# Database Configuration
POSTGRES_DB=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_PORT=54321

# Better Auth Configuration
BETTER_AUTH_SECRET=your-super-secret-key-change-in-production-min-32-chars
BETTER_AUTH_URL=http://localhost:3000
DATABASE_URL=postgres://postgres:password@localhost:54321/postgres

# Social Auth Providers (optional)
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
```

**Important**: Update the database credentials to match your PostgreSQL setup.

### 4. Database Migration

Apply the database schema:

```bash
# Generate and apply migrations
npx drizzle-kit push --config=base/db/drizzle.config.ts
```

### 5. Start the Application

Start the development server:

```bash
# Start the server (recommended)
bun run dev

# Alternative: Start with comprehensive health checks
bun start
```

The application will be available at: **http://localhost:3000**

**Note**: If you get a "port in use" error, kill any existing processes:
```bash
lsof -ti:3000 | xargs kill
```

## Available Scripts

| Command | Description |
|---------|-------------|
| `bun start` | Start with health checks and comprehensive logging |
| `bun run dev` | Start server directly |
| `bun run db:reset` | Reset database and seed with sample data |
| `bun run db:seed` | Seed database with sample data |
| `bun run auth:admin` | Create default admin user |
| `bun run auth:test` | Test authentication flow |

## Verification Steps

### 1. Server Health Check

After starting, verify the server is healthy:

```bash
curl http://localhost:3000/api/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2025-01-01T00:00:00.000Z",
  "services": {
    "auth": "ready",
    "rpc": "ready"
  }
}
```

### 2. Frontend Access

Open http://localhost:3000 in your browser. You should see:

- **TaskBuddy** header with logo
- **Theme toggle** button (light/dark mode)
- **Sign In** and **Sign Up** buttons (when not authenticated)
- **Todo application** interface

### 3. Authentication State

When not logged in, verify:

- Header shows "Sign In" and "Sign Up" buttons
- No user menu or profile information is displayed
- Protected content shows appropriate fallback messages
- Auth state shows `isAuthenticated: false`

### 4. Database Verification

Check that all tables were created:

```bash
# Connect to PostgreSQL
psql postgres://postgres:password@localhost:54321/postgres

# List tables
\dt

# Expected tables:
# - users
# - sessions  
# - accounts
# - verifications
# - two_factors
# - apikeys
# - todos
```

## Authentication Features

### User Registration

1. Click **"Sign Up"** in the header
2. Fill in the registration form:
   - Email (required)
   - Password (minimum 8 characters)
   - Full Name (required)
   - Username (optional)
3. Submit to create account

### User Login

1. Click **"Sign In"** in the header
2. Enter email and password
3. Submit to authenticate

### Admin User

Create a default admin user:

```bash
bun run auth:admin --email <EMAIL> --password adminpassword123
```

Default credentials:
- **Email**: <EMAIL>
- **Password**: adminpassword123

**⚠️ Change these credentials in production!**

## Troubleshooting

### Common Issues

#### 1. Database Connection Failed

**Error**: `Connection refused` or `Database not found`

**Solution**:
- Ensure PostgreSQL is running
- Verify database credentials in `.env`
- Create the database if it doesn't exist

#### 2. Port Already in Use

**Error**: `EADDRINUSE: address already in use :::3000`

**Solution**:
- Kill the process using port 3000: `lsof -ti:3000 | xargs kill`
- Or change the port in `.env`: `SERVER_PORT=3001`

#### 3. Migration Errors

**Error**: Schema conflicts or foreign key violations

**Solution**:
- Reset the database: `bun run db:reset`
- Reapply migrations: `npx drizzle-kit push --config=base/db/drizzle.config.ts`

#### 4. Better Auth API Errors

**Error**: Authentication endpoints not working

**Solution**:
- Check server logs for detailed error messages
- Verify `BETTER_AUTH_SECRET` is set and at least 32 characters
- Ensure database schema is up to date

#### 5. Frontend Not Loading

**Error**: Blank page or JavaScript errors

**Solution**:
- Check browser console for errors
- Verify server is running on correct port
- Clear browser cache and reload

### Debug Mode

Enable detailed logging by setting the log level:

```bash
# Set environment variable for debug logging
export LOG_LEVEL=debug
bun start
```

### Reset Everything

If you encounter persistent issues:

```bash
# 1. Stop the server (Ctrl+C)

# 2. Reset database
bun run db:reset

# 3. Reapply migrations
npx drizzle-kit push --config=base/db/drizzle.config.ts

# 4. Restart server
bun start
```

## Development Workflow

### Making Changes

1. **Backend Changes**: Modify files in `mod/backend/` or `base/`
2. **Frontend Changes**: Modify files in `mod/frontend/`
3. **Database Changes**: Update `mod/backend/schema.ts` and run migrations
4. **Auth Changes**: Update `mod/backend/auth.ts` and related files

### Testing Changes

1. **Manual Testing**: Use the browser interface
2. **API Testing**: Use curl or Postman for API endpoints
3. **Auth Testing**: Use `bun run auth:test` command
4. **Database Testing**: Use `bun run db:reset` to test with fresh data

### Hot Reloading

The server automatically restarts when you make changes to backend files. Frontend changes require a browser refresh.

## Next Steps

Once you have the application running:

1. **Explore the Interface**: Try creating todos, switching themes
2. **Test Authentication**: Register a new user, log in/out
3. **Check Admin Features**: Create an admin user and explore admin-only content
4. **Customize**: Modify components, add features, extend functionality
5. **Deploy**: Follow deployment guides for production setup

## Getting Help

If you encounter issues:

1. Check the server logs for detailed error messages
2. Review this documentation for common solutions
3. Check the [Better Auth documentation](https://better-auth.com) for auth-specific issues
4. Review the [Scaf architecture documentation](./architecture-overview.md) for understanding the codebase

The application follows established patterns and should be straightforward to extend and customize for your specific needs.
