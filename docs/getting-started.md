# Getting Started with Scaf

This guide will help you quickly set up and start working with the Scaf codebase.

## Prerequisites

- [Bun](https://bun.sh/) (required for package management and running the application)
- [PostgreSQL](https://www.postgresql.org/) (required for the database)

## Quick Setup

1. **Clone the repository**

```bash
git clone https://github.com/tini-works/scaf.git
cd scaf
```

2. **Install dependencies**

```bash
bun install
```

> [!IMPORTANT]
> Always use <PERSON><PERSON> as the package manager for this project. Do not use npm or yarn.

3. **Set up environment variables**

Create a `.env` file in the root directory with the following content:

```
DATABASE_URL=postgres://username:password@localhost:5432/scaf
```

Replace `username`, `password`, and database name as needed.

4. **Initialize the database**

```bash
bun run script db:migrate
bun run script db:seed
```

5. **Start the development server**

```bash
bun run script dev
```

The application should now be running at [http://localhost:3000](http://localhost:3000).

## Project Structure

The Scaf codebase follows a modular architecture with a clear separation between reusable infrastructure (`base`) and application-specific code (`mod`).

```
/
├── base/           # Reusable infrastructure
│   ├── backend/    # Backend infrastructure
│   ├── cmds/       # Base commands
│   ├── db/         # Database infrastructure
│   ├── dual/       # Shared code (frontend/backend)
│   └── utils/      # Utility functions
├── mod/            # Application-specific code
│   ├── backend/    # Backend implementation
│   │   ├── routes.ts   # RPC endpoint implementations
│   │   ├── schema.ts   # Database schema
│   │   └── shapes.ts   # Database shapes
│   ├── cmds/       # Application commands
│   ├── dual/       # Shared types and RPC definitions
│   │   ├── index.types.zod.ts  # Shared type definitions
│   │   └── rpc.ts              # RPC endpoint definitions
│   └── frontend/   # Frontend implementation
│       ├── components/     # React components
│       ├── pumped.client.ts  # RPC client
│       ├── pumped.todo.ts    # Todo state and controllers
│       └── pumped.theme.ts   # Theme state
```

## Next Steps

- Learn about the [Development Workflows](./development-workflows.md) for adding features and fixing bugs
- Understand the [Architecture Overview](./architecture-overview.md) to get a deeper understanding of the codebase
- Explore the [State Management](./state-management.md) to learn about the pumped-fn pattern
- Check out the [API Integration](./api-integration.md) to understand how the frontend and backend communicate

