# Better Auth Integration

This document describes the comprehensive Better Auth integration in the Scaf project, providing secure authentication and authorization capabilities.

## Overview

Better Auth has been integrated following Scaf's architectural patterns:
- **Base-Mod Separation**: Reusable auth utilities in `base/`, app-specific auth logic in `mod/`
- **Pumped-fn State Management**: Auth state uses `provide`/`derive` patterns
- **Type Safety**: Zod schemas ensure type safety across client-server boundary
- **DaisyUI Components**: Responsive auth UI components
- **PostgreSQL Integration**: Drizzle ORM with Better Auth tables

## Architecture

### Backend Components

#### 1. Auth Configuration (`mod/backend/auth.ts`)
- Better Auth instance with Drizzle adapter
- Email/password authentication
- Social providers (GitHub, Google) ready for configuration
- Plugins: admin, twoFactor, username, apiKey
- Session management and security settings

#### 2. Database Schema (`mod/backend/schema.ts`)
- **users**: Extended with Better Auth fields (email_verified, two_factor_enabled, etc.)
- **sessions**: User sessions with expiration and metadata
- **accounts**: OAuth and password accounts
- **verifications**: Email verification and password reset tokens
- **two_factors**: 2FA secrets and backup codes
- **apikeys**: API key management with rate limiting

#### 3. RPC Endpoints (`mod/dual/rpc.ts`)
- `authEndpoint`: register, login, logout, getSession, changePassword, updateProfile
- `apiKeyEndpoint`: createApiKey, listApiKeys, deleteApiKey
- Type-safe with Zod validation

#### 4. Route Handlers (`mod/backend/routes.ts`)
- Better Auth API integration
- Session management
- Error handling and logging

### Frontend Components

#### 1. Auth State Management (`mod/frontend/pumped.auth.ts`)
- Reactive auth state using pumped-fn patterns
- Actions: login, register, logout, checkAuth, changePassword, updateProfile
- Derived state: isAuthenticated, isAdmin, userDisplayName
- Error handling and loading states

#### 2. Auth Client (`mod/frontend/auth-client.ts`)
- Better Auth client with plugins
- Type inference from server configuration
- Client-side auth operations

#### 3. UI Components (`mod/frontend/components/auth/`)
- **LoginForm**: Email/password login with validation
- **RegisterForm**: User registration with confirmation
- **AuthGuard**: Route protection and role-based access
- **UserMenu**: User profile dropdown with actions
- **AuthModal**: Modal for login/register forms

#### 4. Header Integration (`mod/frontend/components/Header.tsx`)
- Auth status display
- Login/register buttons for unauthenticated users
- User menu for authenticated users

## Setup and Configuration

### 1. Environment Variables

Add to `.env`:
```env
# Better Auth Configuration
BETTER_AUTH_SECRET=your-super-secret-key-change-in-production-min-32-chars
BETTER_AUTH_URL=http://localhost:3000
DATABASE_URL=postgres://postgres:password@localhost:54321/postgres

# Social Auth Providers (optional)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### 2. Database Migration

The database schema has been updated to include Better Auth tables. Migration was applied using Drizzle:

```bash
npx drizzle-kit generate --config=base/db/drizzle.config.ts
npx drizzle-kit push --config=base/db/drizzle.config.ts
```

### 3. Create Default Admin User

```bash
bun run script auth createAdmin --email <EMAIL> --password admin123 --name "Admin User"
```

## Usage

### Authentication Flow

1. **Registration**: Users can register with email, password, name, and optional username
2. **Login**: Email/password authentication with session creation
3. **Session Management**: Automatic session validation and renewal
4. **Logout**: Secure session termination

### Role-Based Access Control

- **User Role**: Default role for new users
- **Admin Role**: Administrative privileges
- **Role Checking**: Use `isAdmin` derived state for UI conditionals
- **Route Protection**: Use `AuthGuard` and `AdminGuard` components

### API Authentication

API endpoints can be protected using:
- Session-based authentication (cookies)
- API keys for programmatic access
- Bearer tokens for mobile/SPA applications

## Available Commands

### Auth Management Commands (`mod/cmds/auth.ts`)

```bash
# Create admin user
bun run script auth createAdmin --email <EMAIL> --password securepass

# Test authentication flow
bun run script auth testAuth

# List all users (admin)
bun run script auth listUsers

# Reset user password (admin)
bun run script auth resetPassword --email <EMAIL> --password newpass
```

## Security Features

### 1. Session Security
- Secure session tokens
- Configurable expiration (7 days default)
- IP address and user agent tracking
- Session invalidation on logout

### 2. Password Security
- Minimum length requirements
- Secure password hashing
- Password change functionality

### 3. Two-Factor Authentication
- TOTP support via authenticator apps
- Backup codes for recovery
- Optional enforcement per user

### 4. API Key Management
- Rate limiting per key
- Expiration dates
- Permission scoping
- Usage tracking

## Extensibility

### 1. Social Providers

To enable social authentication:

1. Configure provider credentials in `.env`
2. Update `mod/backend/auth.ts` to enable providers:
```typescript
socialProviders: {
  github: {
    clientId: process.env.GITHUB_CLIENT_ID!,
    clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    enabled: true, // Enable when ready
  },
}
```

### 2. Additional Plugins

Better Auth supports many plugins. To add new ones:

1. Install the plugin: `bun add @better-auth/plugin-name`
2. Add to server config in `mod/backend/auth.ts`
3. Add client plugin to `mod/frontend/auth-client.ts`
4. Update types if needed

### 3. Custom Fields

To add custom user fields:

1. Update database schema in `mod/backend/schema.ts`
2. Update Zod schemas in `mod/dual/index.types.zod.ts`
3. Generate and apply migration
4. Update UI components as needed

## Integration Examples

### Protected Route Component

```typescript
import { AuthGuard } from '@/mod/frontend/components/auth/AuthGuard';

const ProtectedPage = () => (
  <AuthGuard>
    <div>This content requires authentication</div>
  </AuthGuard>
);
```

### Admin-Only Component

```typescript
import { AdminGuard } from '@/mod/frontend/components/auth/AuthGuard';

const AdminPanel = () => (
  <AdminGuard>
    <div>Admin-only content</div>
  </AdminGuard>
);
```

### Using Auth State

```typescript
import { useResolves } from '@pumped-fn/react';
import { authState } from '@/mod/frontend/pumped.auth';

const MyComponent = () => {
  const [user, isAuthenticated, login] = useResolves(
    authState.currentUser.reactive,
    authState.isAuthenticated.reactive,
    authState.login.reactive
  );

  if (!isAuthenticated) {
    return <button onClick={() => login({ email: '...', password: '...' })}>Login</button>;
  }

  return <div>Welcome, {user?.name}!</div>;
};
```

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure PostgreSQL is running and credentials are correct
2. **Session Issues**: Check `BETTER_AUTH_SECRET` is set and consistent
3. **CORS Issues**: Verify `trustedOrigins` in auth configuration
4. **Migration Errors**: Reset database if schema conflicts occur

### Debug Mode

Enable debug logging by setting log level in your environment or auth configuration.

## Future Enhancements

1. **Email Verification**: Enable email verification for new registrations
2. **Password Reset**: Implement forgot password flow
3. **Slack Integration**: Add Slack OAuth and bot integration
4. **Multi-Factor Authentication**: Enforce 2FA for admin users
5. **Audit Logging**: Track authentication events and admin actions
6. **Rate Limiting**: Implement login attempt rate limiting
7. **Session Management**: Admin interface for session management

## Security Considerations

1. **Production Secrets**: Change default secrets in production
2. **HTTPS**: Use HTTPS in production for secure cookie transmission
3. **Database Security**: Secure database access and use connection pooling
4. **Regular Updates**: Keep Better Auth and dependencies updated
5. **Monitoring**: Implement authentication event monitoring
6. **Backup**: Regular backup of user data and sessions

This integration provides a solid foundation for authentication and authorization in the Scaf project while maintaining the established architectural patterns and extensibility for future enhancements.
