# API Integration

This document explains how the backend and frontend components work together in the Scaf application, with a focus on the RPC mechanism and real-time updates.

## Overview

The Scaf application uses a unified approach to backend-frontend interoperability:

1. **Shared Type Definitions**: Types are defined once and shared between frontend and backend
2. **RPC Mechanism**: A custom RPC (Remote Procedure Call) system for type-safe API calls
3. **Real-time Updates**: Long polling mechanism for keeping the UI in sync with the database
4. **Unified Pattern**: Both frontend and backend use pumped-fn for state management

## Shared Type Definitions

Types are defined in the `mod/dual` directory and shared between frontend and backend:

```typescript
// mod/dual/index.types.zod.ts
import { z } from "zod/v4";

export const UserSchema = z.object({
  id: z.number(),
  email: z.string().email(),
  name: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
});

export const TodoSchema = z.object({
  id: z.number(),
  user_id: z.number(),
  title: z.string(),
  description: z.string().nullable().optional(),
  completed: z.boolean(),
  status: z.enum(['todo', 'in-progress', 'done']),
  created_at: z.date(),
  updated_at: z.date(),
});

export type User = z.input<typeof UserSchema>;
export type Todo = z.input<typeof TodoSchema>;
```

## RPC Endpoint Definitions

The RPC endpoints are defined in the `mod/dual/rpc.ts` file:

```typescript
// mod/dual/rpc.ts
import { createRouter } from "base/dual/rpc";
import { z } from "zod/v4";
import { UserSchema, TodoSchema } from "./index.types.zod";

export const router = createRouter()
  // User endpoints
  .procedure("getUsers", {
    output: z.array(UserSchema),
  })
  .procedure("getUserById", {
    input: z.object({
      id: z.number(),
    }),
    output: UserSchema.nullable(),
  })
  
  // Todo endpoints
  .procedure("getTodos", {
    output: z.array(TodoSchema),
  })
  .procedure("getTodosByUserId", {
    input: z.object({
      user_id: z.number(),
    }),
    output: z.array(TodoSchema),
  })
  .procedure("createTodo", {
    input: z.object({
      user_id: z.number(),
      title: z.string(),
      description: z.string().nullable().optional(),
      status: z.enum(['todo', 'in-progress', 'done']).default('todo'),
    }),
    output: TodoSchema,
  })
  .procedure("updateTodo", {
    input: z.object({
      id: z.number(),
      title: z.string().optional(),
      description: z.string().nullable().optional(),
      completed: z.boolean().optional(),
      status: z.enum(['todo', 'in-progress', 'done']).optional(),
    }),
    output: TodoSchema,
  })
  .procedure("deleteTodo", {
    input: z.object({
      id: z.number(),
    }),
    output: z.object({
      success: z.boolean(),
    }),
  });

// Export type for client
export type Router = typeof router;
```

## Backend Route Implementation

The RPC endpoints are implemented in the `mod/backend/routes.ts` file:

```typescript
// mod/backend/routes.ts
import { router } from "mod/dual/rpc";
import { derive } from "@pumped-fn/core-next";
import { connection } from "base/backend/db";
import { users, todos } from "./schema";
import { eq } from "drizzle-orm";

// Get all users
export const getUsersRoute = router.implements(
  "getUsers",
  derive(
    [connection],
    ([db]) => async () => {
      return await db.select().from(users);
    }
  )
);

// Get user by ID
export const getUserByIdRoute = router.implements(
  "getUserById",
  derive(
    [connection],
    ([db]) => async ({ input }) => {
      const result = await db
        .select()
        .from(users)
        .where(eq(users.id, input.id));
      return result[0] || null;
    }
  )
);

// Get all todos
export const getTodosRoute = router.implements(
  "getTodos",
  derive(
    [connection],
    ([db]) => async () => {
      return await db.select().from(todos);
    }
  )
);

// Get todos by user ID
export const getTodosByUserIdRoute = router.implements(
  "getTodosByUserId",
  derive(
    [connection],
    ([db]) => async ({ input }) => {
      return await db
        .select()
        .from(todos)
        .where(eq(todos.user_id, input.user_id));
    }
  )
);

// Create todo
export const createTodoRoute = router.implements(
  "createTodo",
  derive(
    [connection],
    ([db]) => async ({ input }) => {
      const now = new Date();
      const result = await db
        .insert(todos)
        .values({
          ...input,
          completed: false,
          created_at: now,
          updated_at: now,
        })
        .returning();
      return result[0];
    }
  )
);

// Update todo
export const updateTodoRoute = router.implements(
  "updateTodo",
  derive(
    [connection],
    ([db]) => async ({ input }) => {
      const { id, ...data } = input;
      const result = await db
        .update(todos)
        .set({
          ...data,
          updated_at: new Date(),
        })
        .where(eq(todos.id, id))
        .returning();
      return result[0];
    }
  )
);

// Delete todo
export const deleteTodoRoute = router.implements(
  "deleteTodo",
  derive(
    [connection],
    ([db]) => async ({ input }) => {
      await db
        .delete(todos)
        .where(eq(todos.id, input.id));
      return { success: true };
    }
  )
);
```

## Frontend RPC Client

The frontend uses a client to make RPC calls to the backend:

```typescript
// mod/frontend/pumped.client.ts
import { provide, derive } from "@pumped-fn/core-next";
import { createClient } from "base/frontend/rpc";
import type { Router } from "mod/dual/rpc";

// Create RPC client
export const client = provide(() => 
  createClient<Router>({
    baseUrl: "/api",
    headers: {
      "Content-Type": "application/json",
    },
  })
);

// Example: Fetch todos
export const fetchTodos = derive(
  [client.reactive],
  ([client]) => async () => {
    return await client.getTodos();
  }
);

// Example: Create todo
export const createTodo = derive(
  [client.reactive],
  ([client]) => async (todo) => {
    return await client.createTodo(todo);
  }
);
```

## Frontend State Management

The frontend uses pumped-fn to manage state and make RPC calls:

```typescript
// mod/frontend/pumped.todo.ts
import { provide, derive } from "@pumped-fn/core-next";
import { client } from "./pumped.client";
import type { Todo } from "mod/dual/index.types.zod";

// State
export const todos = provide<Todo[]>(() => []);
export const loading = provide(() => false);
export const error = provide<Error | null>(() => null);

// Fetcher
export const fetchTodos = derive(
  [todos.static, loading.static, error.static, client.reactive],
  ([todosCtl, loadingCtl, errorCtl, client]) => async () => {
    loadingCtl.update(true);
    errorCtl.update(null);
    
    try {
      const result = await client.getTodos();
      todosCtl.update(result);
    } catch (err) {
      errorCtl.update(err as Error);
    } finally {
      loadingCtl.update(false);
    }
  }
);

// Create todo
export const createTodo = derive(
  [client.reactive, fetchTodos],
  ([client, fetchTodos]) => async (todo) => {
    await client.createTodo(todo);
    await fetchTodos();
  }
);

// Update todo
export const updateTodo = derive(
  [client.reactive, fetchTodos],
  ([client, fetchTodos]) => async (todo) => {
    await client.updateTodo(todo);
    await fetchTodos();
  }
);

// Delete todo
export const deleteTodo = derive(
  [client.reactive, fetchTodos],
  ([client, fetchTodos]) => async (id) => {
    await client.deleteTodo({ id });
    await fetchTodos();
  }
);

// Export all
export const todoState = {
  todos,
  loading,
  error,
  fetchTodos,
  createTodo,
  updateTodo,
  deleteTodo,
};
```

## Real-time Updates

The Scaf application uses a long polling mechanism for real-time updates:

```typescript
// mod/frontend/pumped.realtime.ts
import { provide, derive, effect } from "@pumped-fn/core-next";
import { client } from "./pumped.client";
import { todoState } from "./pumped.todo";

// Polling interval in milliseconds
export const pollingInterval = provide(() => 5000);

// Last update timestamp
export const lastUpdate = provide(() => Date.now());

// Polling function
export const poll = derive(
  [pollingInterval.reactive, lastUpdate.static, todoState.fetchTodos],
  ([interval, lastUpdateCtl, fetchTodos]) => async () => {
    await fetchTodos();
    lastUpdateCtl.update(Date.now());
    
    // Schedule next poll
    setTimeout(poll, interval);
  }
);

// Start polling effect
export const startPollingEffect = effect(
  [poll],
  ([poll]) => {
    poll();
  }
);

// Export all
export const realtimeState = {
  pollingInterval,
  lastUpdate,
  poll,
  startPollingEffect,
};
```

## Component Integration

React components use the pumped-fn state to render the UI:

```typescript
// mod/frontend/components/TodoList.tsx
import { Resolves } from "@pumped-fn/react";
import { todoState } from "../pumped.todo";
import { useEffect } from "react";

export const TodoList = () => {
  // Start fetching todos on mount
  useEffect(() => {
    todoState.fetchTodos();
  }, []);
  
  return (
    <Resolves e={[todoState.todos.reactive, todoState.loading.reactive, todoState.error.reactive, todoState.deleteTodo]}>
      {([todos, loading, error, deleteTodo]) => (
        <div>
          {loading && <div>Loading...</div>}
          {error && <div>Error: {error.message}</div>}
          
          <ul>
            {todos.map(todo => (
              <li key={todo.id}>
                {todo.title}
                <button onClick={() => deleteTodo(todo.id)}>Delete</button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Resolves>
  );
};
```

## Error Handling

The Scaf application uses a consistent error handling pattern:

```typescript
// mod/frontend/pumped.error.ts
import { provide, derive } from "@pumped-fn/core-next";

// Error state
export const error = provide<Error | null>(() => null);

// Set error
export const setError = derive(
  [error.static],
  ([errorCtl]) => (err: Error | null) => {
    errorCtl.update(err);
  }
);

// Clear error
export const clearError = derive(
  [error.static],
  ([errorCtl]) => () => {
    errorCtl.update(null);
  }
);

// Error boundary
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  setError: (err: Error) => void
) => {
  return async (...args: T): Promise<R | undefined> => {
    try {
      return await fn(...args);
    } catch (err) {
      setError(err as Error);
      return undefined;
    }
  };
};

// Export all
export const errorState = {
  error,
  setError,
  clearError,
  withErrorHandling,
};
```

## Authentication

The Scaf application uses a simple authentication mechanism:

```typescript
// mod/frontend/pumped.auth.ts
import { provide, derive } from "@pumped-fn/core-next";
import { client } from "./pumped.client";
import type { User } from "mod/dual/index.types.zod";

// Auth state
export const user = provide<User | null>(() => null);
export const isAuthenticated = derive(
  [user.reactive],
  ([user]) => user !== null
);

// Login
export const login = derive(
  [user.static, client.reactive],
  ([userCtl, client]) => async (email: string, password: string) => {
    const result = await client.login({ email, password });
    userCtl.update(result.user);
    return result;
  }
);

// Logout
export const logout = derive(
  [user.static, client.reactive],
  ([userCtl, client]) => async () => {
    await client.logout();
    userCtl.update(null);
  }
);

// Check auth status
export const checkAuth = derive(
  [user.static, client.reactive],
  ([userCtl, client]) => async () => {
    try {
      const result = await client.me();
      userCtl.update(result);
      return true;
    } catch (err) {
      userCtl.update(null);
      return false;
    }
  }
);

// Export all
export const authState = {
  user,
  isAuthenticated,
  login,
  logout,
  checkAuth,
};
```

## Further Reading

- [State Management](./state-management.md): Details on the pumped-fn pattern
- [Development Workflows](./development-workflows.md): Guide to common development tasks

