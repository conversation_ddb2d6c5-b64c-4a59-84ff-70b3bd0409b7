import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { admin, twoFactor, username, api<PERSON>ey } from "better-auth/plugins";
import { drizzle } from "drizzle-orm/node-postgres";
import * as schema from "./mod/backend/schema";

// This is a configuration file for Better Auth CLI
// It uses a simplified database connection for schema generation

const db = drizzle({
  connection: {
    connectionString: process.env.DATABASE_URL || "postgres://postgres:password@localhost:54321/postgres",
    ssl: false
  },
});

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    usePlural: true,
    schema: {
      users: schema.users,
      sessions: schema.sessions,
      accounts: schema.accounts,
      verifications: schema.verifications,
      twoFactors: schema.two_factors,
      apikeys: schema.apikeys,
    },
  }),
  
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },

  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },

  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID || "",
      clientSecret: process.env.GITHUB_CLIENT_SECRET || "",
      enabled: false,
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
      enabled: false,
    },
  },

  plugins: [
    admin({
      defaultRole: "user",
      adminRole: "admin",
    }),
    twoFactor({
      issuer: "Scaf App",
    }),
    username(),
    apiKey({
      apiKeyHeaders: ["x-api-key", "authorization"],
    }),
  ],

  advanced: {
    database: {
      generateId: () => crypto.randomUUID(),
    },
  },

  secret: process.env.BETTER_AUTH_SECRET || "fallback-secret-change-in-production",
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
  
  trustedOrigins: [
    "http://localhost:3000",
    "http://localhost:5173",
  ],
});
