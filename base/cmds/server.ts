import { derive, provide } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { getServerConfig } from "@/base/backend/config";
import { apiHandler } from "@/base/backend/api-handler";
import { serve } from "bun";

import frontend from "@/mod/frontend/index.html";

const serverConfig = provide(() => getServerConfig());
export default derive(
  [serverConfig, logger('server'), apiHandler],
  async ([config, sLogger, apiHandler], ctl) => {
    sLogger.info(`Starting server on ${config.host}:${config.port}...`);

    const server = serve({
      port: config.port,
      hostname: config.host,
      development: true,
      async fetch(request) {
        const url = new URL(request.url);

        // Handle API routes (both auth and RPC)
        if (url.pathname.startsWith('/api') || url.pathname.startsWith('/rpc')) {
          return apiHandler(request);
        }

        // Serve frontend for all other routes
        return new Response(frontend, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      }
    })

    ctl.cleanup(async () => {
      sLogger.info("Shutting down server...");
      await server.stop();
    })

    await new Promise<void>(() => {})
  }
)