import { defineConfig } from 'drizzle-kit';
import { getDbConfig } from '@/base/db/config';

const dbConfig = getDbConfig(process.env)

export default defineConfig({
  schema: './mod/backend/schema.ts', // Point to the actual schema location
  dialect: "postgresql",
  out: './drizzle',
  dbCredentials: {
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.user,
    password: dbConfig.password,
    database: dbConfig.database,
    ssl: false,
  }
})