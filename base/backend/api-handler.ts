import { derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { auth } from "@/mod/backend/auth";
import { rpc } from "./routes";
import { BunRequest } from "bun";

// Combined API handler that routes between Better Auth and RPC
export const apiHandler = derive(
  [auth, rpc, logger('api')],
  async ([authInstance, rpcHandler, logger], ctl) => {
    logger.info("API handler initialized with Better Auth and RPC support");

    return async (request: BunRequest<string>) => {
      const url = new URL(request.url);
      const pathname = url.pathname;

      logger.debug(`Handling ${request.method} ${pathname}`);

      // Route Better Auth API calls
      if (pathname.startsWith('/api/auth/')) {
        logger.debug('Routing to Better Auth handler');
        
        try {
          // Better Auth expects the path without the /api prefix
          const authPath = pathname.replace('/api', '');
          
          // Create a new request with the modified path
          const authRequest = new Request(
            new URL(authPath, url.origin).toString(),
            {
              method: request.method,
              headers: request.headers,
              body: request.body,
            }
          );

          const response = await authInstance.handler(authRequest);
          return response;
        } catch (error) {
          logger.error('Better Auth handler error:', error);
          return new Response('Internal Server Error', { status: 500 });
        }
      }

      // Route RPC calls
      if (pathname === '/rpc' || pathname === '/api/rpc') {
        logger.debug('Routing to RPC handler');
        return rpcHandler(request);
      }

      // Handle health check
      if (pathname === '/health' || pathname === '/api/health') {
        return Response.json({ 
          status: 'ok', 
          timestamp: new Date().toISOString(),
          services: {
            auth: 'ready',
            rpc: 'ready'
          }
        });
      }

      // Default 404 for unmatched routes
      logger.warn(`No handler found for ${pathname}`);
      return new Response('Not Found', { status: 404 });
    };
  }
);
