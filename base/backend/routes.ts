import * as routes from "@/mod/backend/routes"
import { logger } from "@/base/dual/logger"
import { derive } from "@pumped-fn/core-next"
import { BunRequest } from "bun"

export const rpc = derive(
  [logger('rpc'), ...Object.values(routes)],
  ([logger, ...routes]) => {
    logger.info("RPC server initialized with routes: %O", routes.map(r => r.path).join(", "))

    return async (request: BunRequest<string>) => {
      const url = new URL(request.url)
      const subject = request.headers.get("x-subject") || url.searchParams.get("subject")

      const supportedContentType = request.headers.get("Content-Type") === "application/json"
      if (!supportedContentType) {
        logger.error("Unsupported Content-Type, expected application/json")
        return new Response("Unsupported Content-Type, expected application/json", { status: 415 })
      }

      if (!subject) {
        logger.error("Missing x-subject header")
        return new Response("Missing x-subject header", { status: 400 })
      }

      const route = routes.find(r => r.path === subject)
      if (!route) {
        logger.error(`No route found for subject: ${subject}`)
        return new Response(`No route found for subject: ${subject}`, { status: 404 })
      }

      const handler = route.handler

      const ui = request.body ? await request.json() : undefined
      const vr = route.def.input.safeParse(ui)

      if (!vr.success) {
        logger.debug(`Invalid input for ${subject}:`, vr.error)
        return new Response(`Invalid input for ${subject}`, { status: 400 })
      }

      try {
        const result = await handler({
          input: vr.data as any,
          context: request
        })

        if (!result) {
          return new Response(null, { status: 204, headers: new Headers([["Content-Length", "0"]]) })
        }

        return Response.json(result)
      } catch (error) {
        logger.error(`Error handling ${subject}:`, error)
        return new Response(`Error handling ${subject}`, { status: 500 })
      }
    }
  }
)