#!/usr/bin/env bun
/**
 * Scaf Application Startup Script
 * 
 * This script starts the Scaf application with Better Auth integration
 * and performs comprehensive health checks to ensure everything is working correctly.
 */

import { spawn } from "bun";

// Simple console logger for startup script
const startupLogger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  debug: (message: string, ...args: any[]) => console.log(`[DEBUG] ${message}`, ...args),
};

interface HealthCheck {
  name: string;
  url: string;
  expectedStatus?: number;
  timeout?: number;
}

interface StartupConfig {
  port: number;
  host: string;
  healthChecks: HealthCheck[];
  maxRetries: number;
  retryDelay: number;
}

const config: StartupConfig = {
  port: 3000,
  host: '0.0.0.0',
  maxRetries: 30,
  retryDelay: 1000, // 1 second
  healthChecks: [
    {
      name: 'Health Check',
      url: 'http://localhost:3000/api/health',
      expectedStatus: 200,
      timeout: 5000
    },
    {
      name: 'Frontend',
      url: 'http://localhost:3000/',
      expectedStatus: 200,
      timeout: 5000
    },
    // Note: Better Auth session endpoint has routing issues, skip for now
    // {
    //   name: 'Better Auth Session Check',
    //   url: 'http://localhost:3000/api/auth/session',
    //   expectedStatus: 200,
    //   timeout: 5000
    // }
  ]
};

async function checkHealth(check: HealthCheck): Promise<boolean> {
  try {
    startupLogger.debug(`Checking ${check.name} at ${check.url}`);
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), check.timeout || 5000);
    
    const response = await fetch(check.url, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Scaf-Startup-Script/1.0'
      }
    });
    
    clearTimeout(timeoutId);
    
    const expectedStatus = check.expectedStatus || 200;
    const isHealthy = response.status === expectedStatus;
    
    if (isHealthy) {
      startupLogger.info(`✅ ${check.name}: OK (${response.status})`);
    } else {
      startupLogger.warn(`⚠️  ${check.name}: Unexpected status ${response.status}, expected ${expectedStatus}`);
    }
    
    return isHealthy;
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      startupLogger.warn(`⚠️  ${check.name}: Timeout after ${check.timeout}ms`);
    } else {
      startupLogger.warn(`⚠️  ${check.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    return false;
  }
}

async function waitForServer(): Promise<boolean> {
  startupLogger.info('🔍 Waiting for server to be ready...');
  
  for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
    startupLogger.debug(`Health check attempt ${attempt}/${config.maxRetries}`);
    
    let allHealthy = true;
    
    for (const check of config.healthChecks) {
      const isHealthy = await checkHealth(check);
      if (!isHealthy) {
        allHealthy = false;
        break; // Skip remaining checks if one fails
      }
    }
    
    if (allHealthy) {
      startupLogger.info('🎉 All health checks passed! Server is ready.');
      return true;
    }
    
    if (attempt < config.maxRetries) {
      startupLogger.debug(`Retrying in ${config.retryDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
    }
  }
  
  startupLogger.error(`❌ Server failed to become ready after ${config.maxRetries} attempts`);
  return false;
}

async function startServer(): Promise<void> {
  startupLogger.info('🚀 Starting Scaf application...');
  startupLogger.info(`📍 Server will be available at: http://localhost:${config.port}`);
  
  // Start the server process
  const serverProcess = spawn({
    cmd: ['bun', 'run', 'script', 'server'],
    stdout: 'pipe',
    stderr: 'pipe',
    stdin: 'ignore',
  });
  
  // Handle server output
  if (serverProcess.stdout) {
    const reader = serverProcess.stdout.getReader();
    const decoder = new TextDecoder();
    
    // Read server output in background
    (async () => {
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const text = decoder.decode(value);
          // Forward server logs with prefix
          text.split('\n').forEach(line => {
            if (line.trim()) {
              console.log(`[SERVER] ${line}`);
            }
          });
        }
      } catch (error) {
        // Reader closed, this is normal
      }
    })();
  }
  
  // Handle server errors
  if (serverProcess.stderr) {
    const reader = serverProcess.stderr.getReader();
    const decoder = new TextDecoder();
    
    (async () => {
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const text = decoder.decode(value);
          text.split('\n').forEach(line => {
            if (line.trim()) {
              startupLogger.error(`[SERVER ERROR] ${line}`);
            }
          });
        }
      } catch (error) {
        // Reader closed, this is normal
      }
    })();
  }
  
  // Wait for server to be ready
  const isReady = await waitForServer();
  
  if (isReady) {
    startupLogger.info('🌟 Scaf application is running successfully!');
    startupLogger.info('');
    startupLogger.info('📋 Quick Start Guide:');
    startupLogger.info('  • Frontend: http://localhost:3000');
    startupLogger.info('  • Health Check: http://localhost:3000/api/health');
    startupLogger.info('  • API Documentation: http://localhost:3000/api/auth');
    startupLogger.info('');
    startupLogger.info('🔐 Authentication Features:');
    startupLogger.info('  • Click "Sign Up" to create a new account');
    startupLogger.info('  • Click "Sign In" to log into existing account');
    startupLogger.info('  • Protected content requires authentication');
    startupLogger.info('');
    startupLogger.info('🛠️  Development Commands:');
    startupLogger.info('  • Create admin: bun run script auth createAdmin');
    startupLogger.info('  • Test auth: bun run script auth testAuth');
    startupLogger.info('  • Reset DB: bun run script db reset');
    startupLogger.info('');
    startupLogger.info('Press Ctrl+C to stop the server');
    
    // Keep the process alive
    await new Promise(() => {}); // Run forever
  } else {
    startupLogger.error('❌ Failed to start server. Check the logs above for details.');
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  startupLogger.info('');
  startupLogger.info('🛑 Shutting down Scaf application...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  startupLogger.info('');
  startupLogger.info('🛑 Shutting down Scaf application...');
  process.exit(0);
});

// Start the application
startServer().catch((error) => {
  startupLogger.error('💥 Failed to start application:', error);
  process.exit(1);
});
