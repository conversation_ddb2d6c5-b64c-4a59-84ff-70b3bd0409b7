import { defineConfig } from 'automd'

export default defineConfig({
  // GitHub repository information
  github: {
    repo: 'tini-works/scaf',
    branch: 'main',
  },
  // Base URL for the documentation
  baseURL: '/docs',
  // Markdown parsing and rendering options
  markdown: {
    // Enable GitHub-flavored markdown
    gfm: true,
    // Enable table of contents generation
    toc: true,
    // Enable code highlighting
    highlight: true,
  },
  // Configure example file handling
  examples: {
    // Base directory for example files
    dir: 'examples',
    // Default language for code blocks
    lang: 'typescript',
  },
})

