import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { admin, twoFactor, username, api<PERSON><PERSON> } from "better-auth/plugins";
import { connection } from "@/base/db";
import { derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import * as schema from "@/mod/backend/schema";

// Better Auth configuration using pumped-fn pattern
export const auth = derive(
  [connection, logger('auth')],
  async ([db, logger], ctl) => {
    logger.info('Initializing Better Auth...');

    const authInstance = betterAuth({
      database: drizzleAdapter(db, {
        provider: "pg",
        usePlural: true, // Use plural table names (users, sessions, etc.)
        schema: {
          users: schema.users,
          sessions: schema.sessions,
          accounts: schema.accounts,
          verifications: schema.verifications,
          twoFactors: schema.two_factors,
          apikeys: schema.apikeys,
        },
      }),
      
      // Basic email/password authentication
      emailAndPassword: {
        enabled: true,
        requireEmailVerification: false, // Start simple, can enable later
      },

      // Session configuration
      session: {
        expiresIn: 60 * 60 * 24 * 7, // 7 days
        updateAge: 60 * 60 * 24, // 1 day
      },

      // Social providers (configured but not enabled yet)
      socialProviders: {
        github: {
          clientId: process.env.GITHUB_CLIENT_ID || "",
          clientSecret: process.env.GITHUB_CLIENT_SECRET || "",
          enabled: false, // Will enable when credentials are provided
        },
        google: {
          clientId: process.env.GOOGLE_CLIENT_ID || "",
          clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
          enabled: false, // Will enable when credentials are provided
        },
      },

      // Plugins for extensibility
      plugins: [
        // Admin plugin for user management
        admin({
          defaultRole: "user",
          adminRole: "admin",
        }),
        
        // Two-factor authentication
        twoFactor({
          issuer: "Scaf App",
        }),
        
        // Username support
        username(),
        
        // API key management
        apiKey({
          apiKeyHeaders: ["x-api-key", "authorization"],
        }),
      ],

      // Advanced configuration
      advanced: {
        database: {
          generateId: () => crypto.randomUUID(), // Use UUID for IDs
        },
        crossSubDomainCookies: {
          enabled: false, // Can enable for subdomain support
        },
      },

      // Security settings
      secret: process.env.BETTER_AUTH_SECRET || "fallback-secret-change-in-production",
      baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
      
      // Trust proxy for production deployments
      trustedOrigins: [
        "http://localhost:3000",
        "http://localhost:5173", // Vite dev server
      ],
    });

    logger.info('Better Auth initialized successfully');
    return authInstance;
  }
);

// Export auth instance type for client-side inference
export type AuthInstance = Awaited<ReturnType<typeof auth.resolve>>;
