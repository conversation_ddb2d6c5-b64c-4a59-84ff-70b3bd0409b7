import { connection } from '@/base/db';
import { logger } from '@/base/dual/logger';
import { derive } from '@pumped-fn/core-next';
import { impl } from "@pumped-fn/extra";
import { BunRequest } from 'bun';
import { and, eq } from 'drizzle-orm';
import { custom } from 'zod/v4-mini';
import { endpoint, ShapeOutput } from '@/mod/dual/rpc';
import { todoShape, userShape } from "@/mod/backend/shapes"
import { todos, users } from '@/mod/backend/schema';
import { auth } from '@/mod/backend/auth';

const bunContext = custom<BunRequest<string>>()
const router = impl.service(endpoint).context(bunContext)

export const shapeRoute = router.implements(
  'getShape',
  derive(
    [logger('shapes')],
    ([logger], ctl) => async ({ input }): Promise<ShapeOutput> => {
      const [rUserShape, rTodoShape] = await Promise.all([
        ctl.scope.resolve(userShape),
        ctl.scope.resolve(todoShape)
      ])
      .catch(e => {
        throw new Response(`Error resolving shapes: ${e.message}`, { status: 500 })
      })

      if (input?.incremental) {
        const shapes = [rUserShape, rTodoShape]

        const cleanups: (() => void)[] = []
        await new Promise<void>((resolve) => {

          for (const shape of shapes) {
            const cleanup = shape.subscribe(() => {
              resolve()
            })

            cleanups.push(cleanup)
          }
        })
        .finally(() => {
          for (const cleanup of cleanups) {
            cleanup()
          }
        })
      }

      const latestUsers = await rUserShape.rows
      const latestTodos = await rTodoShape.rows

      return {
        users: latestUsers,
        todos: latestTodos
      } as any
    }
  )
)

export const createUserRoute = router.implements(
  'createUser',
  derive(
    [connection, logger('route.createUser')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Creating user with input:', input);

      await db.insert(users).values(input)    

    }
  )
)

export const createTodoRoute = router.implements(
  'createTodo',
  derive(
    [connection, logger('route.createTodo')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Creating todo with input:', input);

      await db.insert(todos).values(input)    

    }
  )
)

export const changeTodoStatusRoute = router.implements(
  'changeStatus',
  derive(
    [connection, logger('route.markTodoAsDone')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Marking todo as done with input:', input);

      await db.update(todos)
        .set({ status: input.status })
        .where(
          and(
            eq(todos.id, input.id),
            eq(todos.user_id, input.user_id)
          )
        )
    }
  )
)

export const deleteTodoRoute = router.implements(
  'deleteTodo',
  derive(
    [connection, logger('route.deleteTodo')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Deleting todo with input:', input);

      await db.delete(todos).where(eq(todos.id, input))
    }
  )
)

// Auth routes using Better Auth
export const registerRoute = router.implements(
  'register',
  derive(
    [auth, logger('route.register')],
    async ([authInstance, logger]) => async ({ input }) => {
      logger.debug('Registering user with input:', { email: input.email, name: input.name });

      // Use Better Auth's sign up method
      const result = await authInstance.api.signUpEmail({
        body: {
          email: input.email,
          password: input.password,
          name: input.name,
          username: input.username,
        }
      });

      if (!result.user) {
        throw new Error('Registration failed');
      }

      logger.info('User registered successfully:', result.user.id);
      return result.user;
    }
  )
)

export const loginRoute = router.implements(
  'login',
  derive(
    [auth, logger('route.login')],
    async ([authInstance, logger]) => async ({ input }) => {
      logger.debug('Logging in user with email:', input.email);

      // Use Better Auth's sign in method
      const result = await authInstance.api.signInEmail({
        body: {
          email: input.email,
          password: input.password,
        }
      });

      if (!result.user) {
        throw new Error('Login failed');
      }

      logger.info('User logged in successfully:', result.user.id);
      return result.user;
    }
  )
)

export const logoutRoute = router.implements(
  'logout',
  derive(
    [auth, logger('route.logout')],
    async ([authInstance, logger]) => async ({ input, context }) => {
      logger.debug('Logging out user');

      // Extract session token from request headers
      const authHeader = context.headers.get('authorization') || context.headers.get('cookie');

      if (authHeader) {
        await authInstance.api.signOut({
          headers: {
            authorization: authHeader,
            cookie: context.headers.get('cookie') || '',
          }
        });
      }

      logger.info('User logged out successfully');
    }
  )
)

export const getSessionRoute = router.implements(
  'getSession',
  derive(
    [auth, logger('route.getSession')],
    async ([authInstance, logger]) => async ({ input, context }) => {
      logger.debug('Getting session');

      try {
        // Extract session from request headers
        const authHeader = context.headers.get('authorization') || context.headers.get('cookie');

        if (!authHeader) {
          return { user: null, session: null };
        }

        const result = await authInstance.api.getSession({
          headers: {
            authorization: authHeader,
            cookie: context.headers.get('cookie') || '',
          }
        });

        logger.debug('Session retrieved:', {
          authenticated: !!result.user,
          userId: result.user?.id
        });

        return {
          user: result.user,
          session: result.session,
        };
      } catch (error) {
        logger.debug('Session retrieval failed:', error);
        return { user: null, session: null };
      }
    }
  )
)

export const changePasswordRoute = router.implements(
  'changePassword',
  derive(
    [auth, logger('route.changePassword')],
    async ([authInstance, logger]) => async ({ input, context }) => {
      logger.debug('Changing password');

      const authHeader = context.headers.get('authorization') || context.headers.get('cookie');

      if (!authHeader) {
        throw new Error('Authentication required');
      }

      await authInstance.api.changePassword({
        body: {
          currentPassword: input.current_password,
          newPassword: input.new_password,
        },
        headers: {
          authorization: authHeader,
          cookie: context.headers.get('cookie') || '',
        }
      });

      logger.info('Password changed successfully');
    }
  )
)

export const updateProfileRoute = router.implements(
  'updateProfile',
  derive(
    [auth, logger('route.updateProfile')],
    async ([authInstance, logger]) => async ({ input, context }) => {
      logger.debug('Updating profile');

      const authHeader = context.headers.get('authorization') || context.headers.get('cookie');

      if (!authHeader) {
        throw new Error('Authentication required');
      }

      const result = await authInstance.api.updateUser({
        body: input,
        headers: {
          authorization: authHeader,
          cookie: context.headers.get('cookie') || '',
        }
      });

      if (!result.user) {
        throw new Error('Profile update failed');
      }

      logger.info('Profile updated successfully:', result.user.id);
      return result.user;
    }
  )
)
