import { relations, SQL } from "drizzle-orm";
import { pgTable, text, timestamp, boolean, index, unique, PgDialect, pgEnum, integer } from "drizzle-orm/pg-core";

// Better Auth compatible users table (matches generated schema)
export const users = pgTable("users", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  email_verified: boolean("email_verified").$defaultFn(() => false).notNull(),
  image: text("image"),
  created_at: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updated_at: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
  role: text("role"),
  banned: boolean("banned"),
  ban_reason: text("ban_reason"),
  ban_expires: timestamp("ban_expires"),
  two_factor_enabled: boolean("two_factor_enabled"),
  username: text("username").unique(),
  display_username: text("display_username"),
}, (table) => [
  unique("users_email_key").on(table.email),
  unique("users_username_key").on(table.username),
  index("users_email_idx").on(table.email),
  index("users_id_idx").on(table.id),
  index("users_username_idx").on(table.username),
]);

// Better Auth sessions table (matches generated schema)
export const sessions = pgTable("sessions", {
  id: text("id").primaryKey(),
  expires_at: timestamp("expires_at").notNull(),
  token: text("token").notNull().unique(),
  created_at: timestamp("created_at").notNull(),
  updated_at: timestamp("updated_at").notNull(),
  ip_address: text("ip_address"),
  user_agent: text("user_agent"),
  user_id: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  impersonated_by: text("impersonated_by"),
}, (table) => [
  unique("sessions_token_key").on(table.token),
  index("sessions_user_id_idx").on(table.user_id),
  index("sessions_token_idx").on(table.token),
]);

// Better Auth accounts table (matches generated schema)
export const accounts = pgTable("accounts", {
  id: text("id").primaryKey(),
  account_id: text("account_id").notNull(),
  provider_id: text("provider_id").notNull(),
  user_id: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  access_token: text("access_token"),
  refresh_token: text("refresh_token"),
  id_token: text("id_token"),
  access_token_expires_at: timestamp("access_token_expires_at"),
  refresh_token_expires_at: timestamp("refresh_token_expires_at"),
  scope: text("scope"),
  password: text("password"),
  created_at: timestamp("created_at").notNull(),
  updated_at: timestamp("updated_at").notNull(),
}, (table) => [
  index("accounts_user_id_idx").on(table.user_id),
  unique("accounts_provider_account_key").on(table.provider_id, table.account_id),
]);

// Better Auth verifications table (matches generated schema)
export const verifications = pgTable("verifications", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expires_at: timestamp("expires_at").notNull(),
  created_at: timestamp("created_at").$defaultFn(() => new Date()),
  updated_at: timestamp("updated_at").$defaultFn(() => new Date()),
}, (table) => [
  index("verifications_identifier_idx").on(table.identifier),
  index("verifications_value_idx").on(table.value),
]);

// Better Auth two factors table (matches generated schema)
export const two_factors = pgTable("two_factors", {
  id: text("id").primaryKey(),
  secret: text("secret").notNull(),
  backup_codes: text("backup_codes").notNull(),
  user_id: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
}, (table) => [
  index("two_factors_user_id_idx").on(table.user_id),
]);

// Better Auth API keys table (matches generated schema with full features)
export const apikeys = pgTable("apikeys", {
  id: text("id").primaryKey(),
  name: text("name"),
  start: text("start"),
  prefix: text("prefix"),
  key: text("key").notNull(),
  user_id: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  refill_interval: integer("refill_interval"),
  refill_amount: integer("refill_amount"),
  last_refill_at: timestamp("last_refill_at"),
  enabled: boolean("enabled").default(true),
  rate_limit_enabled: boolean("rate_limit_enabled").default(true),
  rate_limit_time_window: integer("rate_limit_time_window").default(86400000),
  rate_limit_max: integer("rate_limit_max").default(10),
  request_count: integer("request_count"),
  remaining: integer("remaining"),
  last_request: timestamp("last_request"),
  expires_at: timestamp("expires_at"),
  created_at: timestamp("created_at").notNull(),
  updated_at: timestamp("updated_at").notNull(),
  permissions: text("permissions"),
  metadata: text("metadata"),
}, (table) => [
  index("apikeys_user_id_idx").on(table.user_id),
  index("apikeys_key_idx").on(table.key),
]);

export const todoStatusEnum = pgEnum("status", ["todo", "in-progress", "done"]);

export const todos = pgTable("todos", {
  id: text("id").notNull().primaryKey(), // Changed to text for consistency
  user_id: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  description: text("description"),
  status: todoStatusEnum().default("todo").notNull(),
  completed: boolean("completed").default(false).notNull(),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
}, (table) => [
  index("todos_user_id_idx").on(table.user_id),
]);

// Relations
export const userRelations = relations(users, ({ many }) => ({
  todos: many(todos),
  sessions: many(sessions),
  accounts: many(accounts),
  two_factors: many(two_factors),
  apikeys: many(apikeys),
}));

export const todoRelations = relations(todos, ({ one }) => ({
  user: one(users, {
    fields: [todos.user_id],
    references: [users.id],
  }),
}));

export const sessionRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.user_id],
    references: [users.id],
  }),
}));

export const accountRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.user_id],
    references: [users.id],
  }),
}));

export const twoFactorRelations = relations(two_factors, ({ one }) => ({
  user: one(users, {
    fields: [two_factors.user_id],
    references: [users.id],
  }),
}));

export const apikeyRelations = relations(apikeys, ({ one }) => ({
  user: one(users, {
    fields: [apikeys.user_id],
    references: [users.id],
  }),
}));

export const toSql = (sql: SQL<unknown>): string => {
  const dialect = new PgDialect()

  return dialect.sqlToQuery(sql).sql
}

