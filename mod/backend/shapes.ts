import { getElectricConfig } from "@/base/backend/config"
import type { selects } from "@/base/db/index.types"
import { logger } from "@/base/dual/logger"
import { isChangeMessage, Row, Shape, ShapeStream, ShapeStreamOptions } from "@electric-sql/client"
import { derive, provide, type Core } from "@pumped-fn/core-next"

const config = provide(() => getElectricConfig())

const shapeCache = new Map<string, Core.Executor<Shape<Row<unknown>>>>()

const createShape = <T>(arg: ShapeStreamOptions['params']): Core.Executor<Shape<Row<T>>> => {
  const shapeKey = `${arg?.table}::[${arg?.where ?? '*'}::${arg?.params ?? '-'}]`
  if (shapeCache.has(shapeKey)) {
    return shapeCache.get(shapeKey)! as Core.Executor<Shape<Row<T>>>
  }

  const newShape = derive(
    [config, logger(shapeKey)],
    async ([config, logger], ctl) => {
      const endpoint = `http://${config.host}:${config.port}/v1/shape`
      logger.info(`Creating shape for ${shapeKey} at ${endpoint}`)

      const stream = new ShapeStream<Row<T>>({
        params: arg,
        url: `http://${config.host}:${config.port}/v1/shape`,
      })

      const cleanPeek = stream.subscribe(ms => {
        ms.forEach(m => {
          if (isChangeMessage(m)) {
            logger.info(`%s - %s:`, m.headers.operation, m.key)
          }
        })
      })

      const shape = new Shape(stream)

      logger.debug(`Awaiting shape to be ready`)

      await shape.rows
      logger.info(`Shape is ready`)

      ctl.cleanup(() => {
        cleanPeek()
        shape.unsubscribeAll()
      })

      return shape
    })

  shapeCache.set(shapeKey, newShape)

  return newShape
}

export const userShape = createShape<selects['users']>({
  table: 'users',
})

export const todoShape = createShape<selects['todos']>({
  table: "todos",
})