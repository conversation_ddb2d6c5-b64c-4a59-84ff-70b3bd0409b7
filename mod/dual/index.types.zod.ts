/* generated code */
import { z } from "zod/v4";

// User schemas with Better Auth fields
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string(),
  email_verified: z.boolean(),
  image: z.string().nullable().optional(),
  created_at: z.date(),
  updated_at: z.date(),
  two_factor_enabled: z.boolean().nullable().optional(),
  username: z.string().nullable().optional(),
  role: z.string(),
});

export const UserInsertSchema = z.object({
  email: z.string().email(),
  name: z.string(),
  username: z.string().optional(),
  role: z.string().default("user"),
});

export const UserUpdateSchema = z.object({
  id: z.string(),
  email: z.string().email().optional(),
  name: z.string().optional(),
  username: z.string().nullable().optional(),
  role: z.string().optional(),
});

// Session schemas
export const SessionSchema = z.object({
  id: z.string(),
  expires_at: z.date(),
  token: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  ip_address: z.string().nullable().optional(),
  user_agent: z.string().nullable().optional(),
  user_id: z.string(),
});

// Account schemas
export const AccountSchema = z.object({
  id: z.string(),
  account_id: z.string(),
  provider_id: z.string(),
  user_id: z.string(),
  access_token: z.string().nullable().optional(),
  refresh_token: z.string().nullable().optional(),
  id_token: z.string().nullable().optional(),
  access_token_expires_at: z.date().nullable().optional(),
  refresh_token_expires_at: z.date().nullable().optional(),
  scope: z.string().nullable().optional(),
  password: z.string().nullable().optional(),
  created_at: z.date(),
  updated_at: z.date(),
});

// Todo schemas updated for text IDs
export const TodoSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  title: z.string(),
  description: z.string().nullable().optional(),
  completed: z.boolean(),
  status: z.enum(['todo', 'in-progress', 'done']),
  created_at: z.date(),
  updated_at: z.date(),
});

export const TodoInsertSchema = z.object({
  user_id: z.string(),
  title: z.string(),
  description: z.string().nullable().optional(),
  completed: z.boolean().default(false),
  status: z.enum(['todo', 'in-progress', 'done']).default('todo'),
});

export const TodoUpdateSchema = z.object({
  id: z.string(),
  title: z.string().optional(),
  description: z.string().nullable().optional(),
  completed: z.boolean().optional(),
  status: z.enum(['todo', 'in-progress', 'done']).optional(),
});

// Auth-specific schemas
export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().min(1),
  username: z.string().optional(),
});

export const ChangePasswordSchema = z.object({
  current_password: z.string(),
  new_password: z.string().min(6),
});

// API Key schemas
export const ApiKeySchema = z.object({
  id: z.string(),
  name: z.string(),
  key: z.string(),
  user_id: z.string(),
  expires_at: z.date().nullable().optional(),
  created_at: z.date(),
  updated_at: z.date(),
});

export const ApiKeyCreateSchema = z.object({
  name: z.string().min(1),
  expires_at: z.date().optional(),
});

// Type exports
export type User = z.infer<typeof UserSchema>;
export type UserInsert = z.infer<typeof UserInsertSchema>;
export type UserUpdate = z.infer<typeof UserUpdateSchema>;
export type Session = z.infer<typeof SessionSchema>;
export type Account = z.infer<typeof AccountSchema>;
export type Todo = z.infer<typeof TodoSchema>;
export type TodoInsert = z.infer<typeof TodoInsertSchema>;
export type TodoUpdate = z.infer<typeof TodoUpdateSchema>;
export type Login = z.infer<typeof LoginSchema>;
export type Register = z.infer<typeof RegisterSchema>;
export type ChangePassword = z.infer<typeof ChangePasswordSchema>;
export type ApiKey = z.infer<typeof ApiKeySchema>;
export type ApiKeyCreate = z.infer<typeof ApiKeyCreateSchema>;

