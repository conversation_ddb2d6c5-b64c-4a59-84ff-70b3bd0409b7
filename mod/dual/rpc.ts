import { define } from "@pumped-fn/extra"
import * as schema from "./index.types.zod"
import { z } from "zod/v4"

// Auth endpoints
export const authEndpoint = define.service({
  register: {
    input: schema.RegisterSchema,
    output: schema.UserSchema,
  },
  login: {
    input: schema.LoginSchema,
    output: schema.UserSchema,
  },
  logout: {
    input: z.void(),
    output: z.void(),
  },
  getSession: {
    input: z.void(),
    output: z.object({
      user: schema.UserSchema.nullable(),
      session: schema.SessionSchema.nullable(),
    }),
  },
  changePassword: {
    input: schema.ChangePasswordSchema,
    output: z.void(),
  },
  updateProfile: {
    input: schema.UserUpdateSchema.omit({ id: true }),
    output: schema.UserSchema,
  },
})

export const userEndpoint = define.service({
  createUser: {
    input: schema.UserInsertSchema,
    output: schema.UserSchema,
  },
  updateUser: {
    input: schema.UserUpdateSchema,
    output: schema.UserSchema,
  },
  deleteUser: {
    input: z.string(),
    output: z.void(),
  },
  getUsers: {
    input: z.void(),
    output: z.array(schema.UserSchema),
  },
})

export const todoEndpoint = define.service({
  createTodo: {
    input: schema.TodoInsertSchema,
    output: schema.TodoSchema,
  },
  updateTodo: {
    input: schema.TodoUpdateSchema,
    output: schema.TodoSchema,
  },
  changeStatus: {
    input: schema.TodoSchema.pick({ status: true, id: true, user_id: true }),
    output: z.void()
  },
  deleteTodo: {
    input: z.string(), // Changed from number to string
    output: z.void(),
  },
  getUserTodos: {
    input: z.string(), // user_id
    output: z.array(schema.TodoSchema),
  },
})

// API Key endpoints
export const apiKeyEndpoint = define.service({
  createApiKey: {
    input: schema.ApiKeyCreateSchema,
    output: schema.ApiKeySchema,
  },
  listApiKeys: {
    input: z.void(),
    output: z.array(schema.ApiKeySchema.omit({ key: true })), // Don't return the actual key
  },
  deleteApiKey: {
    input: z.string(), // api key id
    output: z.void(),
  },
})

const initialSchema = z.object({
  users: z.array(schema.UserSchema),
  todos: z.array(schema.TodoSchema),
})

export type ShapeOutput = z.infer<typeof initialSchema>

export const shapeEndpoint = define.service({
  getShape: {
    input: z.object({
      incremental: z.boolean()
    }).optional(),
    output: initialSchema
  }
})

export const endpoint = define.service({
  ...authEndpoint,
  ...userEndpoint,
  ...todoEndpoint,
  ...apiKeyEndpoint,
  ...shapeEndpoint
})

