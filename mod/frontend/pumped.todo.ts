import { logger } from "@/base/dual/logger";
import { derive, provide } from "@pumped-fn/core-next";

import { Todo, TodoInsert } from "../dual/index.types.zod";
import { caller } from "./pumped.client";

const store = derive(
  [caller, logger('store')],
  async ([caller, logger]) => {
    const data = await caller('getShape')
    logger.info("Store initialized with data:", data);
    return data
  }
)

const updateStoreEffect = derive(
  [store.static, caller],
  ([store, caller], ctl) => {
    let running = true

    async function longPolling() {
      if (!running) return;

      try {
        const nextData = await caller('getShape', { incremental: true });
        store.update(nextData);
        // Schedule the next poll after processing the current response
        setTimeout(longPolling, 0);
      } catch (error) {
        console.error("Error in long polling:", error);
        // If there's an error, retry after a short delay
        setTimeout(longPolling, 0);
      }
    }

    // Start the long polling process
    longPolling();

    ctl.cleanup(() => { running = false })
  }
)

export const users = derive([store.reactive], ([store]) => store.users)
export const todos = derive([store.reactive], ([store]) => store.todos)


export const selectedUserId = provide(() => undefined as undefined | Number)
export const selectedUser = derive([users.reactive, selectedUserId.reactive], ([users, userId]) => {
  if (userId === undefined) return null;
  return users?.find(user => user.id === userId) || null;
})

export const setSelectedUser = derive([selectedUserId.static], ([userIdCtl]) => {
  return (id: number) => {
    userIdCtl.update(id)
  }
})

export const userTodos = derive(
  [selectedUserId.reactive, todos.reactive],
  ([userId, todos]) => {
    if (!userId) return [];
    // Update to use user_id instead of userId
    return todos.filter(todo => todo.user_id === userId);
  }
)

type KanbanColumn = {
  id: 'todo' | 'in-progress' | 'done';
  title: string;
  todos: Todo[];
}

export const kanbanColumns = derive(
  [userTodos.reactive],
  ([todos]) => {
    const columns: KanbanColumn[] = [
      { id: 'todo', title: 'To Do', todos: [] },
      { id: 'in-progress', title: 'In Progress', todos: [] },
      { id: 'done', title: 'Done', todos: [] }
    ];

    // Distribute todos into columns based on status
    todos.forEach(todo => {
      const column = columns.find(col => col.id === todo.status);
      if (column) {
        column.todos.push(todo);
      } else {
        // Fallback for todos without status
        if (todo.completed) {
          columns.find(col => col.id === 'done')?.todos.push(todo);
        } else {
          columns.find(col => col.id === 'todo')?.todos.push(todo);
        }
      }
    });

    return columns;
  }
)

export const todosCtl = derive(
  [todos.static, selectedUser.static, logger('todosCtl'), caller],
  ([todosCtl, selectedUser, logger, caller]) => ({
    addTodo: (todoData: TodoInsert) => {
      const currentUser = selectedUser.get();
      if (!currentUser) {
        logger.warn("No user selected, cannot add todo");
        return;
      }

      logger.info("Adding todo for user:", currentUser.name, "with data:", todoData);
      caller('createTodo', todoData)
    },
    changeStatus: async (todoId: number, newStatus: Todo['status']) => {
      logger.info("Toggling todo status for ID:", todoId);
      const todo = todosCtl.get()?.find(todo => todo.id === todoId);
      if (!todo) {
        logger.warn("Todo not found for ID:", todoId);
        return;
      }

      // Update to use user_id instead of userId in the RPC call
      await caller('changeStatus', { id: todoId, status: newStatus, user_id: todo.user_id });
    },
    deleteTodo: async (todoId: number) => {
      logger.info("Deleting todo with ID:", todoId);
      await caller('deleteTodo', todoId);
    },
    moveTodo: (todoId: number, newStatus: 'todo' | 'in-progress' | 'done') => {
      logger.info(`Moving todo ${todoId} to ${newStatus}`);
      const userId = selectedUser.get()!.id
      // Update to use user_id instead of userId in the RPC call
      caller('changeStatus', { id: todoId, user_id: userId, status: newStatus as Todo['status'] })
    },
    reorderTodo: (sourceIndex: number, destinationIndex: number, sourceColumnId: string, destinationColumnId: string) => {
      logger.info(`Reordering todo from ${sourceColumnId}[${sourceIndex}] to ${destinationColumnId}[${destinationIndex}]`);

      // If moving between columns, use moveTodo
      if (sourceColumnId !== destinationColumnId) {
        const todos = todosCtl.get();
        // Update to use user_id instead of userId
        const userTodosList = todos?.filter(todo => todo.user_id === selectedUser.get()?.id);
        const sourceColumn = userTodosList?.filter(todo => todo.status === sourceColumnId);

        if (sourceColumn?.[sourceIndex]) {
          const todoId = sourceColumn[sourceIndex].id;
          const userId = selectedUser.get()!.id

          // Update to use user_id instead of userId in the RPC call
          caller('changeStatus', { id: todoId, user_id: userId, status: destinationColumnId as Todo['status'] })
        }
      }
      // Reordering within the same column would be handled by the UI
    }
  })
)

export const app = {
  store,
  updateStoreEffect,
  users,
  todos,
  selectedUser,
  setSelectedUser,
  userTodos,
  kanbanColumns,
  todosCtl
}
