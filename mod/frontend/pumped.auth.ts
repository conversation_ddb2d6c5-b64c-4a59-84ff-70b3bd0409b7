import { provide, derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { caller } from "./pumped.client";
import type { User, Session, Login, Register, ChangePassword } from "@/mod/dual/index.types.zod";

// Auth state providers
export const currentUser = provide<User | null>(() => null);
export const currentSession = provide<Session | null>(() => null);
export const authLoading = provide<boolean>(() => false);
export const authError = provide<string | null>(() => null);

// Derived auth state
export const isAuthenticated = derive(
  [currentUser.reactive],
  ([user]) => user !== null
);

export const isAdmin = derive(
  [currentUser.reactive],
  ([user]) => user?.role === "admin"
);

export const userDisplayName = derive(
  [currentUser.reactive],
  ([user]) => {
    if (!user) return null;
    return user.display_username || user.username || user.name;
  }
);

// Auth actions
export const login = derive(
  [caller, currentUser.static, currentSession.static, authLoading.static, authError.static, logger('auth.login')],
  ([caller, userCtl, sessionCtl, loadingCtl, errorCtl, logger]) => 
    async (credentials: Login) => {
      try {
        loadingCtl.update(true);
        errorCtl.update(null);
        logger.info('Attempting login for:', credentials.email);

        const user = await caller.login(credentials);
        
        // Get session after successful login
        const sessionData = await caller.getSession();
        
        userCtl.update(user);
        sessionCtl.update(sessionData.session);
        
        logger.info('Login successful for user:', user.id);
        return user;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Login failed';
        logger.error('Login failed:', errorMessage);
        errorCtl.update(errorMessage);
        throw error;
      } finally {
        loadingCtl.update(false);
      }
    }
);

export const register = derive(
  [caller, currentUser.static, currentSession.static, authLoading.static, authError.static, logger('auth.register')],
  ([caller, userCtl, sessionCtl, loadingCtl, errorCtl, logger]) => 
    async (userData: Register) => {
      try {
        loadingCtl.update(true);
        errorCtl.update(null);
        logger.info('Attempting registration for:', userData.email);

        const user = await caller.register(userData);
        
        // Get session after successful registration
        const sessionData = await caller.getSession();
        
        userCtl.update(user);
        sessionCtl.update(sessionData.session);
        
        logger.info('Registration successful for user:', user.id);
        return user;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Registration failed';
        logger.error('Registration failed:', errorMessage);
        errorCtl.update(errorMessage);
        throw error;
      } finally {
        loadingCtl.update(false);
      }
    }
);

export const logout = derive(
  [caller, currentUser.static, currentSession.static, authLoading.static, authError.static, logger('auth.logout')],
  ([caller, userCtl, sessionCtl, loadingCtl, errorCtl, logger]) => 
    async () => {
      try {
        loadingCtl.update(true);
        errorCtl.update(null);
        logger.info('Attempting logout');

        await caller.logout();
        
        userCtl.update(null);
        sessionCtl.update(null);
        
        logger.info('Logout successful');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Logout failed';
        logger.error('Logout failed:', errorMessage);
        errorCtl.update(errorMessage);
        throw error;
      } finally {
        loadingCtl.update(false);
      }
    }
);

export const checkAuth = derive(
  [caller, currentUser.static, currentSession.static, authLoading.static, authError.static, logger('auth.check')],
  ([caller, userCtl, sessionCtl, loadingCtl, errorCtl, logger]) => 
    async () => {
      try {
        loadingCtl.update(true);
        errorCtl.update(null);
        logger.debug('Checking authentication status');

        const sessionData = await caller.getSession();
        
        userCtl.update(sessionData.user);
        sessionCtl.update(sessionData.session);
        
        logger.debug('Auth check completed:', { 
          authenticated: !!sessionData.user,
          userId: sessionData.user?.id 
        });
        
        return !!sessionData.user;
      } catch (error) {
        logger.debug('Auth check failed, user not authenticated');
        userCtl.update(null);
        sessionCtl.update(null);
        return false;
      } finally {
        loadingCtl.update(false);
      }
    }
);

export const changePassword = derive(
  [caller, authLoading.static, authError.static, logger('auth.changePassword')],
  ([caller, loadingCtl, errorCtl, logger]) => 
    async (passwordData: ChangePassword) => {
      try {
        loadingCtl.update(true);
        errorCtl.update(null);
        logger.info('Attempting password change');

        await caller.changePassword(passwordData);
        
        logger.info('Password change successful');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Password change failed';
        logger.error('Password change failed:', errorMessage);
        errorCtl.update(errorMessage);
        throw error;
      } finally {
        loadingCtl.update(false);
      }
    }
);

export const updateProfile = derive(
  [caller, currentUser.static, authLoading.static, authError.static, logger('auth.updateProfile')],
  ([caller, userCtl, loadingCtl, errorCtl, logger]) => 
    async (profileData: Partial<User>) => {
      try {
        loadingCtl.update(true);
        errorCtl.update(null);
        logger.info('Attempting profile update');

        const updatedUser = await caller.updateProfile(profileData);
        
        userCtl.update(updatedUser);
        
        logger.info('Profile update successful');
        return updatedUser;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
        logger.error('Profile update failed:', errorMessage);
        errorCtl.update(errorMessage);
        throw error;
      } finally {
        loadingCtl.update(false);
      }
    }
);

// Clear auth error
export const clearAuthError = derive(
  [authError.static],
  ([errorCtl]) => () => {
    errorCtl.update(null);
  }
);

// Auth state bundle for easy consumption
export const authState = {
  // State
  currentUser,
  currentSession,
  authLoading,
  authError,
  
  // Derived state
  isAuthenticated,
  isAdmin,
  userDisplayName,
  
  // Actions
  login,
  register,
  logout,
  checkAuth,
  changePassword,
  updateProfile,
  clearAuthError,
};
