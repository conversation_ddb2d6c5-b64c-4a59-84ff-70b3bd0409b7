import { Suspense, useEffect } from 'react';
import TodoApp from './components/TodoApp';
import { ScopeProvider, useResolves } from "@pumped-fn/react";
import { authState } from './pumped.auth';

// Auth initialization component
function AuthInitializer({ children }: { children: React.ReactNode }) {
  const [checkAuth] = useResolves(authState.checkAuth.reactive);

  useEffect(() => {
    // Check authentication status when app loads
    checkAuth().catch(console.error);
  }, [checkAuth]);

  return <>{children}</>;
}

function App() {
  return (
    <ScopeProvider>
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="loading loading-spinner loading-lg"></div>
        </div>
      }>
        <AuthInitializer>
          <div className="min-h-screen">
            <TodoApp />
          </div>
        </AuthInitializer>
      </Suspense>
    </ScopeProvider>
  );
}

export { App }
