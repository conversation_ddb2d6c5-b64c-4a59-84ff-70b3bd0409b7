import React from 'react';
import { Resolves } from '@pumped-fn/react';
import { users, setSelectedUser, selectedUser } from '../pumped.todo';

const UserSelector: React.FC = () => <Resolves e={[users.reactive, selectedUser.reactive, setSelectedUser]}>
  {([
    users, selectedUser, setSelectedUser
  ]) => (
    <div className="mb-6">
      <div className="card bg-base-100 border border-base-300">
        <div className="card-body p-4">
          <h2 className="card-title text-lg font-medium mb-2 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Select User
          </h2>
          
          <div className="form-control w-full">
            <select 
              className="select select-bordered w-full"
              value={selectedUser?.id || ''}
              onChange={(e) => setSelectedUser(Number(e.target.value))}
              aria-label="Select a user"
            >
              <option value="" disabled>Choose a user</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
          </div>
          
          {selectedUser && (
            <div className="mt-3 text-sm text-base-content/70">
              <p>{selectedUser.email}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )}
</Resolves>;

export default UserSelector;
