import React from 'react';
import { useResolves } from '@pumped-fn/react';
import { authState } from '@/mod/frontend/pumped.auth';

interface UserMenuProps {
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
  onAdminClick?: () => void;
}

export const UserMenu: React.FC<UserMenuProps> = ({ 
  onProfileClick, 
  onSettingsClick, 
  onAdminClick 
}) => {
  const [currentUser, userDisplayName, isAdmin, logout, authLoading] = useResolves(
    authState.currentUser.reactive,
    authState.userDisplayName.reactive,
    authState.isAdmin.reactive,
    authState.logout.reactive,
    authState.authLoading.reactive
  );

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!currentUser) {
    return null;
  }

  return (
    <div className="dropdown dropdown-end">
      <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar">
        <div className="w-10 rounded-full">
          {currentUser.image ? (
            <img 
              alt="User avatar" 
              src={currentUser.image} 
              className="rounded-full"
            />
          ) : (
            <div className="bg-neutral text-neutral-content rounded-full w-10 h-10 flex items-center justify-center">
              <span className="text-lg font-semibold">
                {userDisplayName?.charAt(0).toUpperCase() || 'U'}
              </span>
            </div>
          )}
        </div>
      </div>
      
      <ul tabIndex={0} className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
        {/* User Info */}
        <li className="menu-title">
          <span>{userDisplayName}</span>
          <span className="text-xs opacity-60">{currentUser.email}</span>
        </li>
        
        <div className="divider my-1"></div>
        
        {/* Profile */}
        <li>
          <button 
            onClick={onProfileClick}
            className="justify-between"
          >
            Profile
            <span className="badge badge-sm">Edit</span>
          </button>
        </li>
        
        {/* Settings */}
        <li>
          <button onClick={onSettingsClick}>
            Settings
          </button>
        </li>
        
        {/* Admin Panel (only for admins) */}
        {isAdmin && (
          <li>
            <button 
              onClick={onAdminClick}
              className="text-warning"
            >
              Admin Panel
            </button>
          </li>
        )}
        
        <div className="divider my-1"></div>
        
        {/* Two-Factor Authentication Status */}
        <li className="menu-title">
          <span className="flex items-center gap-2">
            Security
            {currentUser.two_factor_enabled ? (
              <span className="badge badge-success badge-xs">2FA On</span>
            ) : (
              <span className="badge badge-warning badge-xs">2FA Off</span>
            )}
          </span>
        </li>
        
        {/* Role Badge */}
        <li className="menu-title">
          <span className="flex items-center gap-2">
            Role
            <span className={`badge badge-xs ${
              currentUser.role === 'admin' ? 'badge-error' : 'badge-info'
            }`}>
              {currentUser.role}
            </span>
          </span>
        </li>
        
        <div className="divider my-1"></div>
        
        {/* Logout */}
        <li>
          <button 
            onClick={handleLogout}
            disabled={authLoading}
            className={`text-error ${authLoading ? 'loading' : ''}`}
          >
            {authLoading ? 'Signing out...' : 'Logout'}
          </button>
        </li>
      </ul>
    </div>
  );
};
