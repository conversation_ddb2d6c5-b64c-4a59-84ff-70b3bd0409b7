import React, { useState } from 'react';
import { useResolves } from '@pumped-fn/react';
import { authState } from '@/mod/frontend/pumped.auth';
import type { Login } from '@/mod/dual/index.types.zod';

interface LoginFormProps {
  onSuccess?: () => void;
  onSwitchToRegister?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onSwitchToRegister }) => {
  const [formData, setFormData] = useState<Login>({
    email: '',
    password: '',
  });

  const [login, authLoading, authError, clearAuthError] = useResolves(
    authState.login.reactive,
    authState.authLoading.reactive,
    authState.authError.reactive,
    authState.clearAuthError.reactive
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await login(formData);
      onSuccess?.();
    } catch (error) {
      // Error is handled by auth state
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (authError) {
      clearAuthError();
    }
  };

  return (
    <div className="card w-full max-w-md bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-center justify-center mb-6">Sign In</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email Input */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Email</span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              className="input input-bordered w-full"
              required
              disabled={authLoading}
            />
          </div>

          {/* Password Input */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Password</span>
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Enter your password"
              className="input input-bordered w-full"
              required
              disabled={authLoading}
            />
          </div>

          {/* Error Alert */}
          {authError && (
            <div className="alert alert-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{authError}</span>
            </div>
          )}

          {/* Submit Button */}
          <div className="form-control mt-6">
            <button
              type="submit"
              className={`btn btn-primary w-full ${authLoading ? 'loading' : ''}`}
              disabled={authLoading}
            >
              {authLoading ? 'Signing In...' : 'Sign In'}
            </button>
          </div>
        </form>

        {/* Switch to Register */}
        {onSwitchToRegister && (
          <div className="text-center mt-4">
            <p className="text-sm">
              Don't have an account?{' '}
              <button
                type="button"
                onClick={onSwitchToRegister}
                className="link link-primary"
                disabled={authLoading}
              >
                Sign up here
              </button>
            </p>
          </div>
        )}

        {/* Forgot Password Link */}
        <div className="text-center mt-2">
          <button
            type="button"
            className="link link-secondary text-sm"
            disabled={authLoading}
            onClick={() => {
              // TODO: Implement forgot password functionality
              alert('Forgot password functionality coming soon!');
            }}
          >
            Forgot password?
          </button>
        </div>
      </div>
    </div>
  );
};
