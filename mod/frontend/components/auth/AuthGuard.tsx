import React, { useEffect } from 'react';
import { useResolves } from '@pumped-fn/react';
import { authState } from '@/mod/frontend/pumped.auth';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAdmin?: boolean;
  redirectTo?: string;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  fallback, 
  requireAdmin = false,
  redirectTo 
}) => {
  const [isAuthenticated, isAdmin, authLoading, checkAuth] = useResolves(
    authState.isAuthenticated.reactive,
    authState.isAdmin.reactive,
    authState.authLoading.reactive,
    authState.checkAuth.reactive
  );

  // Check authentication status on mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Show loading state while checking auth
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated) {
    if (redirectTo) {
      // In a real app, you'd use a router here
      window.location.href = redirectTo;
      return null;
    }

    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="card w-96 bg-base-100 shadow-xl">
          <div className="card-body text-center">
            <h2 className="card-title justify-center">Authentication Required</h2>
            <p>You need to be signed in to access this page.</p>
            <div className="card-actions justify-center">
              <button 
                className="btn btn-primary"
                onClick={() => {
                  // In a real app, you'd navigate to login page
                  window.location.href = '/auth/login';
                }}
              >
                Sign In
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Check admin requirement
  if (requireAdmin && !isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="card w-96 bg-base-100 shadow-xl">
          <div className="card-body text-center">
            <h2 className="card-title justify-center text-error">Access Denied</h2>
            <p>You don't have permission to access this page.</p>
            <div className="card-actions justify-center">
              <button 
                className="btn btn-outline"
                onClick={() => {
                  // Go back to previous page or home
                  window.history.back();
                }}
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated and authorized
  return <>{children}</>;
};

// Convenience component for admin-only content
export const AdminGuard: React.FC<Omit<AuthGuardProps, 'requireAdmin'>> = (props) => (
  <AuthGuard {...props} requireAdmin={true} />
);
