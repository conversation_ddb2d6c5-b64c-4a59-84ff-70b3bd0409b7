import React, { useState } from 'react';
import { useResolves } from '@pumped-fn/react';
import { authState } from '@/mod/frontend/pumped.auth';
import type { Register } from '@/mod/dual/index.types.zod';

interface RegisterFormProps {
  onSuccess?: () => void;
  onSwitchToLogin?: () => void;
}

export const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess, onSwitchToLogin }) => {
  const [formData, setFormData] = useState<Register>({
    email: '',
    password: '',
    name: '',
    username: '',
  });

  const [confirmPassword, setConfirmPassword] = useState('');

  const [register, authLoading, authError, clearAuthError] = useResolves(
    authState.register.reactive,
    authState.authLoading.reactive,
    authState.authError.reactive,
    authState.clearAuthError.reactive
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate password confirmation
    if (formData.password !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    try {
      await register(formData);
      onSuccess?.();
    } catch (error) {
      // Error is handled by auth state
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    if (name === 'confirmPassword') {
      setConfirmPassword(value);
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error when user starts typing
    if (authError) {
      clearAuthError();
    }
  };

  return (
    <div className="card w-full max-w-md bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-center justify-center mb-6">Create Account</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name Input */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Full Name</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter your full name"
              className="input input-bordered w-full"
              required
              disabled={authLoading}
            />
          </div>

          {/* Email Input */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Email</span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              className="input input-bordered w-full"
              required
              disabled={authLoading}
            />
          </div>

          {/* Username Input */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Username (optional)</span>
            </label>
            <input
              type="text"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="Choose a username"
              className="input input-bordered w-full"
              disabled={authLoading}
            />
          </div>

          {/* Password Input */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Password</span>
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Enter your password"
              className="input input-bordered w-full"
              required
              minLength={6}
              disabled={authLoading}
            />
            <label className="label">
              <span className="label-text-alt">Minimum 6 characters</span>
            </label>
          </div>

          {/* Confirm Password Input */}
          <div className="form-control">
            <label className="label">
              <span className="label-text">Confirm Password</span>
            </label>
            <input
              type="password"
              name="confirmPassword"
              value={confirmPassword}
              onChange={handleInputChange}
              placeholder="Confirm your password"
              className="input input-bordered w-full"
              required
              disabled={authLoading}
            />
          </div>

          {/* Error Alert */}
          {authError && (
            <div className="alert alert-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{authError}</span>
            </div>
          )}

          {/* Submit Button */}
          <div className="form-control mt-6">
            <button
              type="submit"
              className={`btn btn-primary w-full ${authLoading ? 'loading' : ''}`}
              disabled={authLoading}
            >
              {authLoading ? 'Creating Account...' : 'Create Account'}
            </button>
          </div>
        </form>

        {/* Switch to Login */}
        {onSwitchToLogin && (
          <div className="text-center mt-4">
            <p className="text-sm">
              Already have an account?{' '}
              <button
                type="button"
                onClick={onSwitchToLogin}
                className="link link-primary"
                disabled={authLoading}
              >
                Sign in here
              </button>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
