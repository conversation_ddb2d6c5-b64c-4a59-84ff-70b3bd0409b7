import React from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import TodoItem from './TodoItem';
import { app } from '../pumped.todo';
import { useResolves } from '@pumped-fn/react';

const KanbanBoard: React.FC = () => {
  const [selectedUser, columns, todosCtl] = useResolves(
    app.selectedUser.reactive, 
    app.kanbanColumns.reactive,
    app.todosCtl
  );
  
  if (!selectedUser) {
    return (
      <div className="card bg-base-100 p-8 text-center">
        <div className="flex flex-col items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <p className="text-base-content/50 mt-4 text-lg">Please select a user to view their tasks</p>
        </div>
      </div>
    );
  }
  
  if (columns.every(column => column.todos.length === 0)) {
    return (
      <div className="card bg-base-100 p-8 text-center">
        <div className="flex flex-col items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <p className="text-base-content/50 mt-4 text-lg">No tasks found for {selectedUser.name}</p>
          <p className="text-base-content/40 mt-2">Add a new task to get started</p>
        </div>
      </div>
    );
  }
  
  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result;
    
    // Dropped outside the list
    if (!destination) {
      return;
    }
    
    // If the item was dropped in a different position
    if (
      source.droppableId !== destination.droppableId ||
      source.index !== destination.index
    ) {
      todosCtl.reorderTodo(
        source.index,
        destination.index,
        source.droppableId,
        destination.droppableId
      );
    }
  };
  
  return (
    <div>
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {columns.map((column) => (
            <div key={column.id} className="card bg-base-100 border border-base-300">
              <div className="card-body p-4">
                <h3 className="card-title text-lg font-medium mb-4 flex items-center">
                  {column.id === 'todo' && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  )}
                  {column.id === 'in-progress' && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                  {column.id === 'done' && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                  {column.title}
                  <span className="ml-2 badge badge-sm">{column.todos.length}</span>
                </h3>
                
                <Droppable isDropDisabled={false} isCombineEnabled ignoreContainerClipping droppableId={column.id}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`min-h-[200px] rounded-md ${
                        snapshot.isDraggingOver ? 'bg-base-200' : ''
                      }`}
                    >
                      {column.todos.length > 0 ? (
                        <div className="space-y-3">
                          {column.todos.map((todo, index) => (
                            <Draggable key={todo.id} draggableId={`todo-${todo.id}`} index={index}>
                              {(provided, snapshot) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                >
                                  <TodoItem todo={todo} />
                                </div>
                              )}
                            </Draggable>
                          ))}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full py-8">
                          <p className="text-base-content/40 text-sm">No tasks</p>
                        </div>
                      )}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </div>
            </div>
          ))}
        </div>
      </DragDropContext>
    </div>
  );
};

export default KanbanBoard;
