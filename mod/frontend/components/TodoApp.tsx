import React from 'react';
import Header from './Header';
import UserSelector from './UserSelector';
import AddTodoForm from './AddTodoForm';
import KanbanBoard from './KanbanBoard';
import { useResolves } from '@pumped-fn/react';
import { app } from '../pumped.todo';

const TodoApp: React.FC = () => {
  useResolves(app.updateStoreEffect)
  
  return (
    <div className="drawer drawer-end">
      <input id="add-task-drawer" type="checkbox" className="drawer-toggle" />
      
      <div className="drawer-content">
        <div className="min-h-screen bg-base flex flex-col min-w-md">
          <Header />

          <main className="container mx-auto px-4 py-6 max-w-7xl flex-1">
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
              <UserSelector />
              <div className="flex justify-end mb-4">
                {/* Mobile-focused styling for the Add New Task button */}
                <label 
                  htmlFor="add-task-drawer" 
                  className="btn btn-primary drawer-button fixed bottom-6 right-6 z-10 shadow-lg md:static md:shadow-none rounded-full md:rounded-md"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 md:h-5 md:w-5 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span className="hidden md:inline">Add New Task</span>
                </label>
              </div>
              <KanbanBoard />
            </div>
          </main>

          <footer className="footer footer-center p-4 bg-base-300 text-base-content mt-12">
            <div>
              <p className="text-sm opacity-70">
                TaskBuddy &copy; {new Date().getFullYear()} - A clean, minimalist todo app
              </p>
            </div>
          </footer>
        </div>
      </div>
      
      <div className="drawer-side">
        <label htmlFor="add-task-drawer" aria-label="close sidebar" className="drawer-overlay"></label>
        {/* Make drawer larger and fullscreen-like on mobile */}
        <div className="p-4 w-full max-w-md min-h-full bg-base-200 text-base-content">
          <AddTodoForm closeDrawer={() => {
            const checkbox = document.getElementById('add-task-drawer') as HTMLInputElement;
            if (checkbox) checkbox.checked = false;
          }} />
        </div>
      </div>
    </div>
  );
};

export default TodoApp;
