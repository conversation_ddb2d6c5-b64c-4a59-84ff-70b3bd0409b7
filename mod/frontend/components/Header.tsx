import React, { useState } from 'react';
import { useResolves } from '@pumped-fn/react';
import { theme } from '../pumped.theme';
import { authState } from '../pumped.auth';
import { UserMenu } from './auth/UserMenu';
import { AuthModal } from './auth/AuthModal';

const Header: React.FC = () => {
  const [currentTheme, toggleTheme] = useResolves(theme.currentTheme.reactive, theme.switchTheme);
  const [isAuthenticated] = useResolves(authState.isAuthenticated.reactive);

  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  const handleShowLogin = () => {
    setAuthMode('login');
    setShowAuthModal(true);
  };

  const handleShowRegister = () => {
    setAuthMode('register');
    setShowAuthModal(true);
  };

  const handleProfileClick = () => {
    // TODO: Navigate to profile page
    console.log('Navigate to profile');
  };

  const handleSettingsClick = () => {
    // TODO: Navigate to settings page
    console.log('Navigate to settings');
  };

  const handleAdminClick = () => {
    // TODO: Navigate to admin panel
    console.log('Navigate to admin panel');
  };

  return (
    <>
      <header className="py-6">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold">TaskBuddy</h1>
            </div>

            <div className="flex items-center gap-2">
              {/* Theme Toggle */}
              <button
                className="btn btn-circle btn-ghost"
                onClick={toggleTheme}
                aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}
              >
                {currentTheme === 'light' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                )}
              </button>

              {/* Auth Section */}
              {isAuthenticated ? (
                <UserMenu
                  onProfileClick={handleProfileClick}
                  onSettingsClick={handleSettingsClick}
                  onAdminClick={handleAdminClick}
                />
              ) : (
                <div className="flex gap-2">
                  <button
                    className="btn btn-ghost btn-sm"
                    onClick={handleShowLogin}
                  >
                    Sign In
                  </button>
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={handleShowRegister}
                  >
                    Sign Up
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        defaultMode={authMode}
      />
    </>
  );
};

export default Header;

