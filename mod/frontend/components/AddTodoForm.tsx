import { useResolves } from '@pumped-fn/react';
import React, { useState } from 'react';
import { app } from '../pumped.todo';

interface AddTodoFormProps {
  closeDrawer?: () => void;
}

const AddTodoForm: React.FC<AddTodoFormProps> = ({ closeDrawer }) => {
  const [selectedUser, todosCtl] = useResolves(app.selectedUser.reactive, app.todosCtl.reactive);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [status, setStatus] = useState<'todo' | 'in-progress' | 'done'>('todo');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) return;
    if (!selectedUser) return

    todosCtl.addTodo({
      user_id: selectedUser.id,
      title: title.trim(),
      description: description.trim() || undefined,
      status
    });
    
    // Reset form
    setTitle('');
    setDescription('');
    setStatus('todo');
    
    // Close the drawer if closeDrawer function is provided
    if (closeDrawer) {
      closeDrawer();
    }
  };

  if (!selectedUser) {
    return (
      <div className="card bg-base-100 border border-base-300">
        <div className="card-body">
          <h2 className="card-title text-lg font-medium mb-2 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add New Task
          </h2>
          <p className="text-base-content/50 text-sm">Please select a user first</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 border border-base-300 h-full">
      <div className="card-body">
        <div className="flex justify-between items-center mb-6">
          <h2 className="card-title text-xl font-medium flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add New Task
          </h2>
          {closeDrawer && (
            <button 
              onClick={closeDrawer}
              className="btn btn-sm btn-circle btn-ghost"
              aria-label="Close drawer"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text font-medium">Task Title</span>
            </label>
            <input
              type="text"
              placeholder="What needs to be done?"
              className="input input-bordered w-full text-lg"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              aria-label="Task title"
              autoFocus
            />
          </div>
          
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text font-medium">Description</span>
            </label>
            <textarea
              placeholder="Add a description (optional)"
              className="textarea textarea-bordered w-full"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={5}
              aria-label="Task description"
            />
          </div>
          
          <div className="form-control w-full">
            <label className="label">
              <span className="label-text font-medium">Status</span>
            </label>
            <select 
              className="select select-bordered w-full" 
              value={status}
              onChange={(e) => setStatus(e.target.value as 'todo' | 'in-progress' | 'done')}
              aria-label="Task status"
            >
              <option value="todo">To Do</option>
              <option value="in-progress">In Progress</option>
              <option value="done">Done</option>
            </select>
          </div>
          
          <div className="form-control mt-6">
            <button
              type="submit"
              className="btn btn-primary btn-lg w-full"
              disabled={!title.trim()}
              aria-label="Add task"
            >
              Add Task
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddTodoForm;
