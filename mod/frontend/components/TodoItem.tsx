import React from 'react';
import { todosCtl } from '../pumped.todo';
import { Resolves } from '@pumped-fn/react';
import { Todo } from '@/mod/dual/index.types.zod';

interface TodoItemProps {
  todo: Todo;
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

const TodoItem: React.FC<TodoItemProps> = ({ todo }) => {
  return (
    <Resolves e={[todosCtl]}>
      {([todosCtl]) => (
        <div
          className={`card bg-base-100 border-l-4 ${
            todo.completed ? 'border-success/70' : 'border-primary/70'
          }`}
        >
          <div className="card-body p-4">
            <div className="flex items-start gap-3">
              <label className="cursor-pointer flex items-center h-6">
                <input
                  type="checkbox"
                  className="checkbox checkbox-primary checkbox-md"
                  checked={todo.completed}
                  onChange={() => todosCtl.changeStatus(todo.id, 'done')}
                  aria-label={`Mark "${todo.title}" as ${todo.completed ? 'incomplete' : 'complete'}`}
                />
              </label>

              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <h3 className={`text-lg font-medium ${
                    todo.completed ? 'line-through text-base-content/50' : ''
                  }`}>
                    {todo.title}
                  </h3>

                  <div className="flex items-center gap-2">
                    <span className="text-xs text-base-content/50">
                      {formatDate(todo.created_at)}
                    </span>

                    <button
                      className="btn btn-circle btn-xs btn-ghost"
                      onClick={() => todosCtl.deleteTodo(todo.id)}
                      aria-label={`Delete task "${todo.title}"`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>

                {todo.description && (
                  <p className={`text-sm mt-1 ${
                    todo.completed ? 'text-base-content/40' : 'text-base-content/70'
                  }`}>
                    {todo.description}
                  </p>
                )}

                <div className="flex items-center mt-2 justify-between">
                  <div className={`badge badge-sm ${getStatusBadgeClass(todo.status)} gap-1`}>
                    {getStatusIcon(todo.status)}
                    {getStatusText(todo.status)}
                  </div>
                  
                  {/* Drag handle indicator */}
                  <div className="text-base-content/30 cursor-move">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Resolves>
  );
};

// Helper functions for status display
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'todo': return 'badge-primary';
    case 'in-progress': return 'badge-warning';
    case 'done': return 'badge-success';
    default: return 'badge-primary';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'todo': return 'To Do';
    case 'in-progress': return 'In Progress';
    case 'done': return 'Done';
    default: return 'To Do';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'todo':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      );
    case 'in-progress':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    case 'done':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    default:
      return null;
  }
};

export default TodoItem;
