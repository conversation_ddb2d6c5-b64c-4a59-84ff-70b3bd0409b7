import React from 'react';
import { Resolves } from '@pumped-fn/react';
import { userTodos, selectedUser } from '../pumped.todo';

const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return 'Good morning';
  if (hour < 18) return 'Good afternoon';
  return 'Good evening';
};

const HeroSection: React.FC = () => {
  return (
    <Resolves e={[userTodos.reactive, selectedUser.reactive]}>
      {([userTodos, selectedUser]) => (
        <div className="hero bg-base-200 rounded-2xl p-6 mb-8">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h2 className="text-3xl font-bold">
                {selectedUser
                  ? `${getGreeting()}, ${selectedUser.name}!`
                  : 'Welcome to TaskBuddy!'}
              </h2>
              <p className="py-4 text-base-content/80">
                {selectedUser
                  ? "Let's organize your tasks and boost your productivity today."
                  : "Select a user to get started with your tasks."}
              </p>

              <div className="flex justify-center mt-2">
                <div className="stats inline-block">
                  {selectedUser && (
                    <div className="stat place-items-center p-4">
                      <div className="stat-title">Your Tasks</div>
                      <div className="stat-value text-primary text-3xl flex items-center justify-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2M9 12l2 2 4-4" />
                        </svg>
                        <span>{userTodos?.length}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Resolves>
  )
}
export default HeroSection;
