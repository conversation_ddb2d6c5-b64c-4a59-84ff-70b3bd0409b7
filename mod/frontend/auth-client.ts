import { createAuth<PERSON>lient } from "better-auth/client";
import { adminClient, twoFactorClient, usernameClient, apiKeyClient } from "better-auth/client/plugins";
import { inferAdditionalFields } from "better-auth/client/plugins";
import type { AuthInstance } from "@/mod/backend/auth";

// Create Better Auth client with plugins
export const authClient = createAuthClient({
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
  
  plugins: [
    // Infer additional fields from server
    inferAdditionalFields<AuthInstance>(),
    
    // Admin plugin for user management
    adminClient(),
    
    // Two-factor authentication
    twoFactorClient({
      twoFactorPage: "/auth/two-factor", // Redirect page for 2FA verification
    }),
    
    // Username support
    usernameClient(),
    
    // API key management
    apiKeyClient(),
  ],
});

// Export auth client type for type inference
export type AuthClientType = typeof authClient;
