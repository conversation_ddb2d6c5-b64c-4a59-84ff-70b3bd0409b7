@import "tailwindcss";
@plugin "daisyui";

@plugin "daisyui/theme" {
  name: "lofi";
  color-scheme: "light";
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(97% 0 0);
  --color-base-300: oklch(94% 0 0);
  --color-base-content: oklch(0% 0 0);
  --color-primary: oklch(15.906% 0 0);
  --color-primary-content: oklch(100% 0 0);
  --color-secondary: oklch(21.455% 0.001 17.278);
  --color-secondary-content: oklch(100% 0 0);
  --color-accent: oklch(26.861% 0 0);
  --color-accent-content: oklch(100% 0 0);
  --color-neutral: oklch(0% 0 0);
  --color-neutral-content: oklch(100% 0 0);
  --color-info: oklch(79.54% 0.103 205.9);
  --color-info-content: oklch(15.908% 0.02 205.9);
  --color-success: oklch(90.13% 0.153 164.14);
  --color-success-content: oklch(18.026% 0.03 164.14);
  --color-warning: oklch(88.37% 0.135 79.94);
  --color-warning-content: oklch(17.674% 0.027 79.94);
  --color-error: oklch(78.66% 0.15 28.47);
  --color-error-content: oklch(15.732% 0.03 28.47);
  --radius-selector: 0.25rem;
  --radius-field: 0rem;
  --radius-box: 0.25rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
  --noise: 0;
}

body #root {
  @apply min-h-screen;
}

/* Improve accessibility with focus styles */
:focus-visible {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--b3));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--p) / 50%);
}

/* Minimal transitions for essential UI elements only */
button, a {
  transition-property: background-color, border-color;
  transition-duration: 150ms;
  transition-timing-function: ease-in-out;
}

/* Simplified checkbox style */
.checkbox:checked {
  /* No animations */
}
