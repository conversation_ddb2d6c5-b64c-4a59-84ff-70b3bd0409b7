import { connection } from "@/base/db";
import { logger } from "@/base/dual/logger";
import * as schema from "@/mod/backend/schema";
import { todos, users } from "@/mod/backend/schema";
import { derive } from "@pumped-fn/core-next";
import * as seeder from "drizzle-seed";
import { parseArgs } from "node:util";

export const reset = derive(
  [connection, logger('seeder')],
  async ([db, logger]) => {
    logger.info("Seeding database...");
    await seeder.reset(db, { users, todos })
    logger.info("Database reset successfully.");
  }
)

export const seed = derive(
  [connection, logger('seeder')],
  async ([db, logger]) => {
    logger.info("Seeding database...");
    await seeder.seed(db, schema).refine(f => ({
      users: {
        count: 5,
        with: {
          todos: 10
        }
      }
    }))

    logger.info("Database seeded successfully.");
  }
)

export const addUser = derive(
  [connection, logger('seeder')],
  async ([db, logger], ctl) => {
    logger.info("Adding a new user to the database...");

    const args = parseArgs({
      options: {
        name: { type: 'string', short: 'n', default: 'New User' },
        email: { type: 'string', short: 'e', default: '<EMAIL>' }
      },
      allowPositionals: true
    })

    const newUser = args.values

    await db.insert(users).values(newUser);
    logger.info("New user added successfully.");
  }
)

export default seed