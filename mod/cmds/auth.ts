import { derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { auth } from "@/mod/backend/auth";
import { parseArgs } from "node:util";

// Create default admin user
export const createAdmin = derive(
  [auth, logger('auth.createAdmin')],
  async ([authInstance, logger], ctl) => {
    logger.info("Creating default admin user...");

    const args = parseArgs({
      options: {
        email: { type: 'string', short: 'e', default: '<EMAIL>' },
        password: { type: 'string', short: 'p', default: 'adminpassword123' }, // Better Auth requires longer passwords
        name: { type: 'string', short: 'n', default: 'Admin User' },
        username: { type: 'string', short: 'u', default: 'admin' }
      },
      allowPositionals: true
    });

    const { email, password, name, username } = args.values;

    try {
      // Create admin user using Better Auth
      const result = await authInstance.api.signUpEmail({
        body: {
          email: email!,
          password: password!,
          name: name!,
          username: username,
        }
      });

      if (!result.user) {
        throw new Error('Failed to create admin user');
      }

      // Update user role to admin
      const updatedUser = await authInstance.api.updateUser({
        body: {
          role: 'admin',
        },
        headers: {
          // We need to provide session context for the update
          'x-user-id': result.user.id,
        }
      });

      logger.info(`Admin user created successfully:`, {
        id: result.user.id,
        email: result.user.email,
        name: result.user.name,
        username: result.user.username,
        role: 'admin'
      });

      logger.warn(`Default admin credentials:`, {
        email: email,
        password: password,
        note: 'Please change these credentials in production!'
      });

    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        logger.warn('Admin user already exists with email:', email);
      } else {
        logger.error('Failed to create admin user:', error);
        throw error;
      }
    }
  }
);

// List all users (admin command)
export const listUsers = derive(
  [auth, logger('auth.listUsers')],
  async ([authInstance, logger], ctl) => {
    logger.info("Listing all users...");

    try {
      // This would typically require admin authentication
      // For now, we'll use direct database access through the auth instance
      const users = await authInstance.api.listUsers({
        headers: {
          // In a real scenario, this would be an admin session
          'x-admin': 'true',
        }
      });

      logger.info(`Found ${users.length} users:`);
      users.forEach(user => {
        logger.info(`- ${user.name} (${user.email}) - Role: ${user.role || 'user'}`);
      });

    } catch (error) {
      logger.error('Failed to list users:', error);
      throw error;
    }
  }
);

// Reset user password (admin command)
export const resetPassword = derive(
  [auth, logger('auth.resetPassword')],
  async ([authInstance, logger], ctl) => {
    logger.info("Resetting user password...");

    const args = parseArgs({
      options: {
        email: { type: 'string', short: 'e', required: true },
        password: { type: 'string', short: 'p', default: 'newpassword123' }
      },
      allowPositionals: true
    });

    const { email, password } = args.values;

    if (!email) {
      logger.error('Email is required. Use --email or -e flag.');
      return;
    }

    try {
      // This would typically require admin authentication
      // Implementation depends on Better Auth's admin capabilities
      logger.info(`Password reset for ${email} would be implemented here`);
      logger.warn('Password reset functionality requires admin session implementation');
      
    } catch (error) {
      logger.error('Failed to reset password:', error);
      throw error;
    }
  }
);

// Test authentication flow
export const testAuth = derive(
  [auth, logger('auth.test')],
  async ([authInstance, logger], ctl) => {
    logger.info("Testing authentication flow...");

    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123'; // Better Auth requires longer passwords
    const testName = 'Test User';

    try {
      // 1. Create test user
      logger.info('Creating test user...');
      const signUpResult = await authInstance.api.signUpEmail({
        body: {
          email: testEmail,
          password: testPassword,
          name: testName,
        }
      });

      if (!signUpResult.user) {
        throw new Error('Failed to create test user');
      }

      logger.info('Test user created:', signUpResult.user.id);

      // 2. Sign in test user
      logger.info('Signing in test user...');
      const signInResult = await authInstance.api.signInEmail({
        body: {
          email: testEmail,
          password: testPassword,
        }
      });

      if (!signInResult.user || !signInResult.session) {
        throw new Error('Failed to sign in test user');
      }

      logger.info('Test user signed in successfully:', {
        userId: signInResult.user.id,
        sessionId: signInResult.session.id
      });

      // 3. Get session
      logger.info('Getting session...');
      const sessionResult = await authInstance.api.getSession({
        headers: {
          cookie: `better-auth.session_token=${signInResult.session.token}`,
        }
      });

      if (!sessionResult.user || !sessionResult.session) {
        throw new Error('Failed to get session');
      }

      logger.info('Session retrieved successfully:', {
        userId: sessionResult.user.id,
        sessionId: sessionResult.session.id
      });

      // 4. Sign out
      logger.info('Signing out test user...');
      await authInstance.api.signOut({
        headers: {
          cookie: `better-auth.session_token=${signInResult.session.token}`,
        }
      });

      logger.info('Test user signed out successfully');

      logger.info('✅ Authentication flow test completed successfully!');

    } catch (error) {
      logger.error('❌ Authentication flow test failed:', error);
      throw error;
    }
  }
);

export default createAdmin;
