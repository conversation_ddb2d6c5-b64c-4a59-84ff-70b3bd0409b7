// mod/dual/index.types.zod.ts
import { z } from 'zod/v4';

export const FeatureSchema = z.object({
  id: z.number(),
  name: z.string(),
  enabled: z.boolean(),
  user_id: z.number(),
  created_at: z.date(),
  updated_at: z.date(),
});

export const FeatureInsertSchema = FeatureSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true 
});

export type Feature = z.infer<typeof FeatureSchema>;
export type FeatureInsert = z.infer<typeof FeatureInsertSchema>;

