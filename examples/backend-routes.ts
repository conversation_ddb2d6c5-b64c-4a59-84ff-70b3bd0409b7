// mod/backend/routes.ts
import { connection } from '@/base/db';
import { logger } from '@/base/dual/logger';
import { derive } from '@pumped-fn/core-next';
import { impl } from "@pumped-fn/extra";
import { BunRequest } from 'bun';
import { endpoint } from '@/mod/dual/rpc';
import { features } from '@/mod/backend/schema';
import { eq } from 'drizzle-orm';

const bunContext = custom<BunRequest<string>>();
const router = impl.service(endpoint).context(bunContext);

export const createFeatureRoute = router.implements(
  'createFeature',
  derive(
    [connection, logger('route.createFeature')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Creating feature with input:', input);
      await db.insert(features).values(input);
    }
  )
);

export const toggleFeatureRoute = router.implements(
  'toggleFeature',
  derive(
    [connection, logger('route.toggleFeature')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Toggling feature with input:', input);
      await db.update(features)
        .set({ enabled: input.enabled })
        .where(eq(features.id, input.id));
    }
  )
);

