// mod/dual/rpc.ts
import { define } from "@pumped-fn/extra";
import * as schema from "./index.types.zod";
import { z } from "zod/v4";

export const featureEndpoint = define.service({
  createFeature: {
    input: schema.FeatureInsertSchema,
    output: z.void(),
  },
  toggleFeature: {
    input: z.object({
      id: z.number(),
      enabled: z.boolean(),
    }),
    output: z.void(),
  },
  deleteFeature: {
    input: z.number(),
    output: z.void(),
  },
});

