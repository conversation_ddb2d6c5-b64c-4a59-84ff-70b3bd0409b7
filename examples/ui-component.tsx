// mod/frontend/components/FeatureList.tsx
import React from 'react';
import { Resolves } from '@pumped-fn/react';
import * as app from '../pumped.feature';
import { Feature } from '@/mod/dual/index.types.zod';

interface FeatureItemProps {
  feature: Feature;
  onToggle: (id: number, enabled: boolean) => void;
  onDelete: (id: number) => void;
}

const FeatureItem: React.FC<FeatureItemProps> = ({ feature, onToggle, onDelete }) => {
  return (
    <div className="flex items-center justify-between p-4 border-b">
      <div>
        <h3 className="font-medium">{feature.name}</h3>
      </div>
      <div className="flex items-center space-x-4">
        <label className="inline-flex items-center cursor-pointer">
          <input 
            type="checkbox" 
            checked={feature.enabled} 
            onChange={() => onToggle(feature.id, !feature.enabled)}
            className="sr-only peer"
          />
          <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
        </label>
        <button 
          onClick={() => onDelete(feature.id)}
          className="text-red-500 hover:text-red-700"
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export const FeatureList: React.FC = () => {
  return (
    <Resolves e={[app.features.reactive, app.featuresCtl]}>
      {([features, featuresCtl]) => (
        <div className="bg-white shadow rounded-lg">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold">Feature Flags</h2>
          </div>
          <div>
            {features.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No features available
              </div>
            ) : (
              features.map(feature => (
                <FeatureItem 
                  key={feature.id}
                  feature={feature}
                  onToggle={featuresCtl.toggleFeature}
                  onDelete={featuresCtl.deleteFeature}
                />
              ))
            )}
          </div>
        </div>
      )}
    </Resolves>
  );
};

