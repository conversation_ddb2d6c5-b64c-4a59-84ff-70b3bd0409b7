// mod/backend/schema.ts
import { pgTable, serial, text, boolean, timestamp, integer } from 'drizzle-orm/pg-core';

export const featureTable = pgTable('features', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  enabled: boolean('enabled').default(false),
  user_id: integer('user_id').references(() => users.id).notNull(),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
});

