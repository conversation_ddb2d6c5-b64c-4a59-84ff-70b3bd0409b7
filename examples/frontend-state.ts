// mod/frontend/pumped.feature.ts
import { derive } from "@pumped-fn/core-next";
import { caller } from "./pumped.client";
import { logger } from "@/base/dual/logger";
import { store } from "./pumped.store";
import { Feature } from "@/mod/dual/index.types.zod";

// Derive features from the store
export const features = derive(
  [store.reactive], 
  ([store]) => store.features || []
);

// Feature controller for UI interactions
export const featuresCtl = derive(
  [features.static, logger('featuresCtl'), caller],
  ([features, logger, caller]) => ({
    addFeature: async (featureData) => {
      logger.info("Adding feature with data:", featureData);
      await caller('createFeature', featureData);
    },
    toggleFeature: async (featureId: number, enabled: boolean) => {
      logger.info(`Setting feature ${featureId} to ${enabled ? 'enabled' : 'disabled'}`);
      await caller('toggleFeature', { id: featureId, enabled });
    },
    deleteFeature: async (featureId: number) => {
      logger.info("Deleting feature with ID:", featureId);
      await caller('deleteFeature', featureId);
    }
  })
);

