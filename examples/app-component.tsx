// mod/frontend/components/App.tsx
import React from 'react';
import { FeatureList } from './FeatureList';
import { useResolves } from '@pumped-fn/react';
import * as app from '../pumped.feature';

export const App: React.FC = () => {
  // Activate the real-time update effect
  useResolves(app.updateStoreEffect);
  
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Feature Management</h1>
        
        <div className="grid gap-6">
          <FeatureList />
          
          {/* Add more components here */}
        </div>
      </div>
    </div>
  );
};

